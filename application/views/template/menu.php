<script type="text/javascript">
	window.addEventListener("load", function (){
    $.fn.dataTable.ext.errMode = 'none';
    setTimeout(() => {
      $(".table:not(.search):not(.no-data-table)").DataTable({
			paging: false,
			searching: false,
			scrollX: true,
			info: false
		});

    $(".table.search:not(.no-data-table)").DataTable({
			paging: false,
			scrollX: true,
			info: false
		});
    $(".deviation-table").DataTable({
			paging: false,
			scrollX: true,
			info: false,
			order: [[0, 'desc']]
		});
    $(".dated-table").DataTable({
			paging: false,
      searching: false,
			scrollX: true,
			info: false,
			order: [[1, 'desc']]
		});
    }, 500);
	}, false);
  function goBack(fallbackUrl) {
    if (window.document.referrer.includes("create") || window.document.referrer.includes("update")
      || window.document.referrer.includes("add") || 
      (window.document.referrer.includes("documents") && !window.document.referrer.includes("user/documents")
      && !window.document.referrer.includes("reports/critical/documents"))
      || window.document.referrer.includes("tasks") || window.document.referrer.includes("edit")) {
      window.location = fallbackUrl;
    } else {
      window.history.go(-1)
    }
  }
</script>
  <!-- Main Header -->
  <header class="main-header">
    <div class="navbar-custom-menu-left">
    <ul class="nav navbar-nav" style="margin: 0">
      <li class="dropdown">
				<a href="#" class="dropdown-toggle" data-toggle="dropdown">
					<i class="fa fa-th" aria-hidden="true" style="color: #845995"></i>
					<span class="sr-only">Appar</span>
				</a>
				<ul class="dropdown-menu app-box">
          <section class="app-box-header">
            <img style="max-width: 120px" src="<?php echo cdn_url('resources/img/orna-logo.png'); ?>" />
          </section>
          <div class="row">
            <div class="col-sm-6">
              <div style="padding:8px"><?php echo lang('modules') ?></div>
					<li>
						<?php
                $first_menu_id = null;
                foreach($this->menus['structure'][0] as $menu_id => $m) {
                  if( ! isset($this->menus['structure'][$menu_id]) )
                    continue;
                  $first_menu_id = $menu_id;
                  break;
                }
								echo '<div class="navbar-module">' . icon_anchor('perspective', $first_menu_id, '<div style="display: flex"> 
                <img class="navbar-module-img" src="/resources/img/icons/document.svg">
                <div class="col" style="align-self: center;"> 
                <div class="navbar-module-title">' . lang('document_module') . '</div>
                <div class="navbar-module-subtitle">' . lang('document_module_subtitle') . '</div>
                </div>
                </div>'
                , ['title' => 'Dokument']) . 

                '</div>';
						?>
						<?php if( isset($this->mainmenu[1]) && ! empty($this->mainmenu[1]) ): ?>
							<?php foreach($this->mainmenu[1] as $apps): ?>
							<?php if( $apps->name === 'Kvalitetsprotokoll' && ! is_role('Kvalitetssäkrare') ) { continue; } ?>
							<?php $href = ! empty($apps->href) ? $apps->href : base_url('page/') . $apps->page_id; ?>
							<div class="navbar-module">
								<a href="<?php echo $href; ?>">
                  <div style="display: flex">
                    <img class="navbar-module-img" src="/resources/img/icons/<?php 
                      $map = ['fa-user-md' => 'form', 
                      'fa-map-signs' => 'deviation',
                      'fa-exclamation-triangle' => 'eventanalysis',
                      'fa-life-bouy' => 'riskassessment', 
                      'fa-align-justify' => 'checklist',
                      'fa-tasks' => 'task'];
                      echo isset($map[$apps->icon]) ? $map[$apps->icon] : 'default'
                    ?>.svg" alt="">
                    <div class="col" style="align-self: center;">
                      <div class="navbar-module-title"><?php echo html_escape($apps->name); ?> </div>
                      <div class="navbar-module-subtitle"> <?php echo substr($apps->subtitle, 0, 100); ?> </div>
                    </div>
                  </div>
								</a>
              </div>
							<?php endforeach; ?>
						<?php endif; ?>
					</li>
          </div>
          <div class="col-sm-6">
            <div>
              <div style="padding:8px; margin-bottom: 3px"><?php echo lang('manage') ?></div>
              <div class="navbar-manage">
								<a href="/"> <div style="display: flex" class="navbar-links">
                  <img class="navbar-module-img" src="/resources/img/icons/dashboard.svg" alt="">
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('dashboard') ?></div>
                  </div>
                </div></a>
                <a href="/reports/documents"> <div style="display: flex" class="navbar-links">
                  <i class="fa fa-search" aria-hidden="true" style="
                      color: #8f47b3;
                      font-size: 21px;
                      margin-right: 13px;
                      margin-left: 2px;"></i>
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('advaned_search') ?></div>
                  </div>
                </div></a>
                <a href="/user/documents"> <div style="display: flex" class="navbar-links">
                  <img class="navbar-module-img" src="/resources/img/icons/mydocuments.svg" alt="">
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('users_my_documents') ?></div>
                  </div>
                </div></a>
                <a href="/user/staff"> <div style="display: flex" class="navbar-links">
                  <img class="navbar-module-img" src="/resources/img/icons/users.svg" alt="">
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('users_staff') ?></div>
                  </div>
                </div></a>
                <?php if( is_role('Systemadministratör') ): ?>
                  <a href="<?php echo site_url('reports'); ?>"> <div style="display: flex" class="navbar-links">
                    <img class="navbar-module-img" src="/resources/img/icons/reports.svg" alt="">
                    <div class="col" style="align-self: center;">
                      <div class="navbar-module-title"><?php echo lang('reports') ?></div>
                    </div>
                  </div></a>
                <?php endif; ?>
              </div>
              <div style="padding:8px; margin-bottom: 3px; margin-top: 10px"><?php echo lang('quick_links') ?></div>
              <div class="navbar-quick-links">
                <a href="/docs/standards"> <div style="display: flex" class="navbar-links">
                  <i class="fa fa-check-circle" aria-hidden="true" style="
                      color: #b5819f;
                      font-size: 24px;
                      margin-right: 10px;
                      margin-left: 2px;"></i>
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('orna_standards') ?></div>
                  </div>
                </div></a>
                <a href="/docs/help"> <div style="display: flex" class="navbar-links">
                  <img class="navbar-module-img" src="/resources/img/icons/help.svg" alt="">
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('orna_help_guide') ?></div>
                  </div>
                </div></a>
                <a href="/docs/orna_analys_manual"> <div style="display: flex" class="navbar-links">
                  <img class="navbar-module-img" src="/resources/img/icons/help.svg" alt="">
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('orna_analys_manual') ?></div>
                  </div>
                </div></a>
                <a href="/docs/orna_updates"> <div style="display: flex" class="navbar-links">
                  <i class="fa fa-bell" aria-hidden="true" style="color: #B5819F; margin-right: 13px; font-size: 21px;"></i>
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('orna_updates') ?></div>
                  </div>
                </div></a>
                <a href="https://kvalprak.se" target="_blank"> <div style="display: flex" class="navbar-links">
                  <img class="navbar-module-img" src="/resources/img/icons/website.svg" alt="">
                  <div class="col" style="align-self: center;">
                    <div class="navbar-module-title"><?php echo lang('website') ?></div>
                  </div>
                </div></a>
              </div>
            </div>
          </div>
        </div>
				</ul>
			</li>
    </ul>
    </div>
    <!-- Logo -->
    <a href="<?php echo site_url(); ?>" class="logo">
      <!-- mini logo for sidebar mini 50x50 pixels -->
      <span class="logo-mini"><b>K</b> 2</span>
      <!-- logo for regular state and mobile devices -->
     <span class="logo-lg"><b><?php echo $this->config->item('program_name'); ?></b></span>
    </a>

    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top navbar-expand-md" role="navigation">
    <?php if( $this->auth_flex OR $this->auth_kiv ): ?>
	<div class="navbar-header">
		<button type="button" class="navbar-toggler collapsed" data-toggle="collapse" data-target="#navbar-collapse" aria-expanded="false">
			<i class="fa fa-bars"></i>
		</button>
	</div>
	<div class="collapse navbar-collapse float-left" id="navbar-collapse">
		<ul class="nav navbar-nav">
			<?php
			if( $this->auth_kiv ):
				// var_dump($this->menus['structure']);
				$parents = (isset($menu) && is_array($menu) && isset($menu['parents']) && !empty($menu['parents'])) ? TRUE : FALSE;
				if( isset($this->menus['structure'][0]) && !empty($this->menus['structure'][0]) ):
					foreach($this->menus['structure'][0] as $menu_id => $m):
						if( ! isset($this->menus['structure'][$menu_id]) )
							continue;
						$active = ($parents && in_array($menu_id,$menu['parents'])) ? 'class="active"' : '';
						echo '<li ' . $active . '>' . safe_anchor('perspective', $menu_id, preg_replace('/^[0-9\.\s]+/u', '',$m->name)) . '</li>';
					endforeach;
				endif;
			endif;
			?>
		</ul>
	</div>
  <?php endif; // $this->auth_flex OR $this->auth_kiv ?>
      <!-- Navbar Right Menu -->
      <div class="navbar-custom-menu">
        <ul class="nav navbar-nav">
			<li><a href="<?php echo site_url(); ?>">
				<i class="fa fa-home" aria-hidden="true"></i>
				<span class="sr-only">Hem</span>
			</a></li>
      <?php // Add Protokoll for ! Flex ?>
      <?php if( $this->auth_flex OR $this->auth_kiv ): ?>
			<li><a href="<?php echo site_url('documents/search'); ?>">
				<i class="fa fa-search" aria-hidden="true"></i>
				<span class="sr-only">Sök</span>
			</a></li>
		<?php if( $report['warning'] !== 0): ?>
          <!-- Tasks Menu -->
          <li class="dropdown notifications-menu">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="badge badge-warning"><?php echo $report['warning']; ?></span>
            </a>
            <ul class="dropdown-menu">
              <li class="header">Du har <?php echo $report['warning']; ?> <?php echo $report['warning']===1 ? 'uppgift' : 'uppgifter'; ?></li>
              <li>
                <!-- Inner Menu: contains the notifications -->
                <ul class="menu">
				  <?php if( ! empty($report['documents']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/documents'); ?>">
                      <i class="fa fa-file-text-o text-yellow"></i> <?php echo count($report['documents']['warning']); ?> dokument behöver uppdateras
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
				  <?php if( ! empty($report['education']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/education'); ?>">
                      <i class="fa fa-graduation-cap text-yellow"></i> <?php echo count($report['education']['warning']); ?> dokument finns i din utbildningsplan
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
				  <?php if( ! empty($report['messages']['documents']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/draft'); ?>">
                      <i class="fa fa-check text-yellow"></i> <?php echo count($report['messages']['documents']['warning']); ?> dokument behöver godkännas
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
				  <?php if( ! empty($report['messages']['autosave']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/autosave'); ?>">
                      <i class="fa fa-floppy-o text-yellow"></i> <?php echo count($report['messages']['autosave']['warning']); ?> dokument har automatiskt sparats
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
				  <?php if( ! empty($report['deviation']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('deviation') . '#incomplete'; ?>">
                      <i class="fa fa-map-signs text-yellow"></i> <?php echo count($report['deviation']['warning']); ?> avvikelse(r) behöver behandlas
                    </a>
                  </li><!-- end notification -->
          <?php endif; ?>
          <?php if( ! empty($report['messages']['tasks']['warning']) ): ?>
                  <li><!-- start notification -->
                  <a href="<?php echo site_url('reports/warning/tasks'); ?>">
                      <i class="fa fa-floppy-o text-yellow"></i> <?php echo count($report['messages']['tasks']['warning']); ?> mål behöver godkännas 
                  </a>
                  </li><!-- end notification -->
          <?php endif; ?>
				  <?php if( ! empty($report['messages']['eventanalysis']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/eventanalysis'); ?>">
                      <i class="fa fa-exclamation-triangle text-yellow"></i> <?php echo count($report['messages']['eventanalysis']['warning']); ?> händelseanalys(er) behöver besvaras
                    </a>
                  </li><!-- end notification -->
          <?php endif; ?>
				  <?php if( ! empty($report['messages']['eventanalysis_actionlist']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/eventanalysis_actionlist'); ?>">
                      <i class="fa fa-exclamation-triangle text-yellow"></i> <?php echo count($report['messages']['eventanalysis_actionlist']['warning']); ?> händelseanalys(er) behöver åtgärdslistor
                    </a>
                  </li><!-- end notification -->
          <?php endif; ?>
				  <?php if( ! empty($report['messages']['checklist']['warning']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/warning/checklist'); ?>">
                      <i class="fa fa-list-alt text-yellow"></i> <?php echo count($report['messages']['checklist']['warning']); ?> checklist(a/or) behöver fyllas i
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
                </ul>
              </li>
			  <?php /*
              <li class="footer">
                <a href="/reports/warning">Visa alla uppgifter</a>
              </li>
			   */ ?>
            </ul>
          </li>
		<?php endif; ?>
    <?php if( $report['success'] !== 0): ?>
      <!-- Tasks Menu -->
      <li class="dropdown notifications-menu">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="badge badge-success"><?php echo $report['success']; ?></span>
            </a>
            <ul class="dropdown-menu">
              <li class="header">Du har <?php echo $report['success']; ?> <?php echo $report['success']===1 ? 'uppgift' : 'uppgifter'; ?></li>
              <li>
                <!-- Inner Menu: contains the notifications -->
                <ul class="menu">
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/success/draft'); ?>">
                      <i class="fa fa-check text-green"></i> <?php echo count($report['messages']['documents']['success']); ?> dokument accepted
                    </a>
                  </li><!-- end notification -->
                </ul>
              </li>
            </ul>
          </li>
    <?php endif; ?>
		<?php if( $report['critical'] !== 0): ?>
          <!-- Tasks Menu -->
          <li class="dropdown notifications-menu">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="badge badge-danger"><?php echo $report['critical']; ?></span>
            </a>
            <ul class="dropdown-menu">
              <li class="header">Du har <?php echo $report['critical']; ?> <?php echo $report['critical']===1 ? 'uppgift' : 'uppgifter'; ?></li>
              <li>
                <!-- Inner Menu: contains the notifications -->
                <ul class="menu">
				  <?php if( ! empty($report['documents']['critical']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/critical/documents'); ?>">
                      <i class="fa fa-file-text-o text-red"></i> <?php echo count($report['documents']['critical']); ?> dokument har gått ut
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
				  <?php if( ! empty($report['messages']['eventanalysis']['critical']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('eventanalysis'); ?>">
                      <i class="fa fa-exclamation-triangle text-red"></i> <?php echo count($report['messages']['eventanalysis']['critical']); ?> händelseanalys(er) måste skapas
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
				  <?php if( ! empty($report['messages']['documents']['critical']) ): ?>
                  <li><!-- start notification -->
                    <a href="<?php echo site_url('reports/critical/draft'); ?>">
                      <i class="fa fa-times text-red"></i> <?php echo count($report['messages']['documents']['critical']); ?> dokument behöver ändras
                    </a>
                  </li><!-- end notification -->
				  <?php endif; ?>
                </ul>
              </li>
			  <?php /*
              <li class="footer">
                <a href="/reports/critical">Visa alla uppgifter</a>
              </li>
			  */ ?>
            </ul>
          </li>
		  <?php endif; ?>
          <!-- Control Sidebar Toggle Button -->
          <?php if( is_role('Systemadministratör') ): ?>
          <li>
            <a href="<?php echo site_url('admin'); ?>"><i class="fa fa-gears"></i></a>
          </li>
          <?php endif; ?>
      <?php endif; // $this->auth_flex OR $this->auth_kiv ?>
          <!-- User Account Menu -->
          <li class="dropdown user user-menu">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <!-- The user image in the navbar-->
              <i class="fa fa-user" aria-hidden="true"></i>
              <!-- d-none d-sm-block hides the username on small devices so only the image appears. -->
              <span class="d-none d-sm-inline"><?php echo $this->auth_name; ?></span>
            </a>
            <ul class="dropdown-menu">
              <!-- The user image in the menu -->
              <?php /*
              <li class="user-header">
                <img src="<?php echo cdn_url('vendor/adminlte/img/user4-128x128.jpg'); ?>" class="rounded-circle" alt="User Image">

                <p>
                  <?php echo $this->auth_name; ?>
                  <small>Ditt bolag AB - Kiropraktor</small>
                </p>
              </li>
              */ ?>
              <!-- Menu Footer-->
              <li class="user-footer">
                <div class="float-left">
                  <a href="<?php echo site_url('user/staff') ; ?>" class="btn btn-default btn-flat" title="<?php echo lang('users_staff'); ?>"><i class="fa fa-users" aria-hidden="true"></i></a>
                  <a href="<?php echo site_url('user/profile') ; ?>" class="btn btn-default btn-flat" title="<?php echo lang('users_profile'); ?>"><i class="fa fa-user" aria-hidden="true"></i></a>
                  <?php if( $this->auth_flex OR $this->auth_kiv ): ?>
                    <a href="<?php echo site_url('user/documents'); ?>" class="btn btn-default btn-flat" title="Mina arbetsdokument"><i class="fa fa-folder" aria-hidden="true"></i></a>
                    <?php if( is_role('Systemadministratör') ): ?>
                    <a href="<?php echo site_url('reports'); ?>" class="btn btn-default btn-flat" title="Rapporter"><i class="fa fa-file-text" aria-hidden="true"></i></a>
                    <?php endif; ?>
                  <?php endif; // $this->auth_flex OR $this->auth_kiv ?>
                </div>
                <div class="float-right">
                  <a href="<?php echo site_url('login/logout'); ?>" class="btn btn-default btn-flat">Logga ut</a>
                </div>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </nav>
  </header>