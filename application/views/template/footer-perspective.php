<footer class="main-footer no-print">
		<div class="float-right d-none d-sm-block">
			<b>Version</b> <?php echo $this->config->item('program_version'); ?>
		</div>
		<?php echo sprintf($this->config->item('program_copyright'),date('Y')); ?>
	</footer>
</div>
<!-- ./wrapper -->
	<script src="<?php echo cdn_url('vendor/jquery/jquery.min.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/bootstrap/js/bootstrap.bundle.min.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/jquery-slimscroll/jquery.slimscroll.min.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/adminlte/js/adminlte.min.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/jquery-ui/jquery-ui.min.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/datatables/datatables.min.js?v=201'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/ckeditor/ckeditor.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/dropzone/min/dropzone.min.js'); ?>"></script>
	<script src="<?php echo cdn_url('vendor/selectize.js/js/standalone/selectize.min.js'); ?>"></script>
	<?php if( $this->auth_kiv ): ?>
		<script src="<?php echo cdn_url('vendor/elfinder/js/elfinder.min.js?v=2.1.61'); ?>"></script>
	<?php endif; ?>
	<script src="<?php echo cdn_url('resources/js/kvalprak.js?v=12010916'); ?>"></script>
	<script>
		Dropzone.autoDiscover = false;
		Dropzone.prototype.defaultOptions.dictDefaultMessage = "<u>Välj filer</u><br/>(eller drag och släpp dem här)";
		// Dropzone.prototype.defaultOptions.dictFallbackMessage = "Your browser does not support drag'n'drop file uploads.";
		// Dropzone.prototype.defaultOptions.dictFallbackText = "Please use the fallback form below to upload your files like in the olden days.";
		// Dropzone.prototype.defaultOptions.dictFileTooBig = "File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.";
		// Dropzone.prototype.defaultOptions.dictInvalidFileType = "You can't upload files of this type.";
		// Dropzone.prototype.defaultOptions.dictResponseError = "Server responded with {{statusCode}} code.";
		// Dropzone.prototype.defaultOptions.dictCancelUpload = "Cancel upload";
		// Dropzone.prototype.defaultOptions.dictCancelUploadConfirmation = "Are you sure you want to cancel this upload?";
		// Dropzone.prototype.defaultOptions.dictRemoveFile = "Remove file";
		// Dropzone.prototype.defaultOptions.dictMaxFilesExceeded = "You can not upload any more files.";

		$(function() {
			var myDropzone = new Dropzone("div#dropzone", {
				url: "<?php echo base_url('documents/upload'); ?>",
				maxFilesize: 128, // MB
				timeout: 300000, // 6 minutes
				maxFiles: 50,
				sending: function(file, xhr, formData) {
					formData.append('kvalprakcsrf', $('meta[name="kvalprakcsrf"]').attr('content'));
					formData.append('uuid_kvalprak', 'new');
          formData.append('folder_id', '<?php echo isset($folder_id) ? $folder_id : ''; ?>');
				},
				// @STEP2: Add sucess and append a list uploaded files
				success: function(file, response) {
          window.location = "/documents/update/"+response.document_id+"?first=true";
				},
				error: function(file, response) {
					$(file.previewElement).addClass("dz-error").find('.dz-error-message').text(response);
				}
			});

			var body = $('body');
			if( body.width() < 992 ) {
				body.addClass('sidebar-collapse');
				$('.content-header').addClass('notransition');
			}

			$('#documents_editors').selectize();

			if ($("#documents_document").length > 0) {
			// Have changes been made? Autosave!
			$('.content input, .content select, .content textarea').change(function(){
				autosave();
			});
			//Also autosave when user is finished writing in the content box
			CKEDITOR.instances.documents_document.on('contentDom', function() {
				  CKEDITOR.instances.documents_document.document.on('keyup', function(event) {
					  clearTimeout(typingTimer);
					  typingTimer = setTimeout(autosave, doneTypingInterval);
				  });
			});
			}
		});
	</script>
</body>
</html>
