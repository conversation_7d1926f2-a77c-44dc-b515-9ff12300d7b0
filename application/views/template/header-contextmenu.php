<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title><?php echo $this->config->item('program_name'); ?> | <?php echo $this->config->item('program_desc'); ?></title>
	<!-- Tell the browser to be responsive to screen width -->
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<meta name="<?php echo $this->security->get_csrf_token_name(); ?>" content="<?php echo $this->security->get_csrf_hash(); ?>">
	<link rel="icon" href="<?php echo cdn_url('resources/img/favicon/favicon-32x32.png?v=12010915'); ?>" sizes="32x32">
	<link rel="icon" href="<?php echo cdn_url('resources/img/favicon/favicon-192x192.png?v=12010915'); ?>" sizes="192x192">
	<link rel="apple-touch-icon-precomposed" href="<?php echo cdn_url('resources/img/favicon/favicon-180x180.png?v=12010915'); ?>">
	<meta name="msapplication-TileImage" content="<?php echo cdn_url('resources/img/favicon/favicon-270x270.png?v=12010915'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/bootstrap/css/bootstrap.min.css?v=4.6.2'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/font-awesome/css/font-awesome.min.css?v=4.6.2'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/adminlte/css/AdminLTE.min.css?v=4.6.2'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/adminlte/css/skins/_all-skins.min.css?v=4.6.2'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/jquery-ui/jquery-ui.min.css'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/jquery-contextmenu/jquery.contextMenu.min.css'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('vendor/datatables/datatables.min.css?v=2'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('resources/css/kvalprak.css?v=13020930'); ?>">
	<link rel="stylesheet" href="<?php echo cdn_url('resources/css/skin.css?v=13020929'); ?>">
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
</head>
<?php $body_class .=  $sidebar['active']?'':' sidebar-collapse'; ?>
<body class="<?php echo $body_class; ?>">
<div class="wrapper">
<?php
$this->load->view('template/menu');
if( $sidebar['active'] )
	$this->load->view('template/sidebar');