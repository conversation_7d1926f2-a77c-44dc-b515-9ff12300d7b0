<script type="text/javascript">
	let table = null;
	window.addEventListener("load", function (){
		let hashLocation = window.location.hash.substr(1);
		if (!hashLocation) {
			hashLocation = "published"
		}
		$("#"+hashLocation).addClass("active");
		$("#"+hashLocation+"-head").addClass("active");
		$('[data-toggle="tooltip"]').tooltip();
		table = $(".approval-table").DataTable({
			paging: false,
			scrollX: true,
			info: false,
			columnDefs: [
        {
            orderable: false,
            render: DataTable.render.select(),
            targets: 0
        }
      ],
      select: {
          style: 'multi',
          selector: 'td:first-child'
      },
      order: [[1, 'asc']]
		});
		table.on('select', function (e, dt, type, indexes) {
        $("#multi-approve-btn").show()
    })
    .on('deselect', function (e, dt, type, indexes) {
			const selected = table.rows( { selected: true } ).ids();
			if (selected.length == 0)
				$("#multi-approve-btn").hide()
    });
	}, false)
	function changeTab(hashLocation) {
		if(history.pushState) {
			history.pushState(null, null, '#' + hashLocation);
		}
		else {
			location.hash = '#' + hashLocation;
		}
		$(".tabs-head.active").removeClass("active");
		$(".tab-pane.active").removeClass("active");
		$("#"+hashLocation).addClass("active");
		$("#"+hashLocation+"-head").addClass("active");
		DataTable.tables({ visible: true , api: true}).columns.adjust();
	}
	function approveSelected() {
		const selected = table.rows( { selected: true } ).ids().toArray();
		if (selected.length > 0) {
			$.ajax({
				url: "<?php echo base_url('documents/approve_multiple'); ?>",
				method: 'POST',
				type: 'POST',
				data: {
						kvalprakcsrf: $('meta[name="kvalprakcsrf"]').attr('content'),
						selected
					},
				headers: {'X-Requested-With': 'XMLHttpRequest'}
			}).then(response => {
				console.log(response)
				window.location.reload();
			});
		}
	}
</script>

<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<h1>
					<?php echo lang('users_my_documents'); ?>
					<small></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<?php // var_dump($sidebar['menu']['structure']); ?>
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="nav-tabs-custom">
							<ul class="nav nav-tabs">
								<li class="tabs-head" id="published-head"><a onclick="changeTab('published')" data-toggle="tab" aria-expanded="true"><?php echo lang('users_documents_published'); ?></a></li>
								<li class="tabs-head" id="draft-head"><a onclick="changeTab('draft')" data-toggle="tab" aria-expanded="false"><?php echo lang('users_documents_draft'); ?></a></li>
								<li class="tabs-head" id="waiting-approval-head"><a onclick="changeTab('waiting-approval')" data-toggle="tab" aria-expanded="false"><?php echo lang('users_documents_waiting_approval'); ?></a></li>
								<li class="tabs-head" id="unpublished-head"><a onclick="changeTab('unpublished')" data-toggle="tab" aria-expanded="false"><?php echo lang('users_documents_unpublished'); ?></a></li>
							</ul>
							<div class="tab-content no-padding">
								<div class="tab-pane" id="published">
									<?php if( empty($documents['published']) ): ?>
										<p class="text-center padding-10"><?php echo lang('folder_empty'); ?></p>
									<?php else: ?>
										<div class="table-responsive mailbox-messages">
											<table class="table table-hover table-striped search" style="width:100%">
												<thead>
													<tr>
														<th><?php echo lang('documents_document'); ?></th>
														<th><?php echo lang('documents_category'); ?></th>
														<th><?php echo lang('folder_folder'); ?></th>
														<th><?php echo lang('documents_valid_until'); ?></th>
														<th><?php echo lang('documents_owner'); ?></th>
														<th><?php echo lang('documents_edited_date'); ?></th>
														<th data-orderable="false"></th>
													</tr>
												</thead>
												<tbody>
												<?php foreach($documents['published'] as $document): ?>
													<tr>
														<td data-order="<?php echo $document->name; ?>" data-search="<?php echo $document->name; ?>">
														<?php 
														$file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
														($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
														($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
														echo $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->name); ?></td>
														<td><?php if ($document->document_category && array_key_exists($document->document_category, $documents_category)): ?>
															<div class="category-tag"><?php echo $documents_category[$document->document_category]; ?></div>
															<?php endif; ?>
														</td>
														<td><?php echo safe_anchor('perspective', array($document->menu_id, $document->folder_id), $document->folder_name); ?></td>
														<td data-order="<?php echo $document->valid_until; ?>" data-search="<?php echo $document->valid_until; ?>"><?php 
														$one_month = new DateInterval('P1M');
														$one_month_forward = new DateTime();
														$one_month_forward->add($one_month);
														$valid_until = date_create($document->valid_until);
														$flag = (new DateTime() > $valid_until ? "<img src='/resources/img/icons/error.svg' style='margin-left: -5px;
														margin-top: -4px;'/>" : 
														($one_month_forward > $valid_until ? 
														"<i class='fa fa-exclamation-triangle' style='color: #f39c12; margin-right: 5px;'></i>" : 
														"<i class='fa fa-info-circle' style='color: #00a65a; margin-right: 6px;'></i>"));
														echo $document->valid_until === '0000-00-00' ? '' : 
															($flag . ' ' . $document->valid_until) ; ?></td>
														<td><?php echo username($document->owner); ?></td>
														<td><?php echo substr($document->edited_date, 0, 10); ?></td>
														<td>
															<div class="table-options">
																<?php
																echo icon_anchor('documents/update', $document->document_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
																	array(
																	'title' => lang('edit'),
																	'class' => 'btn btn-light',
																	)
																);
																?>
															</div>
														</td>
													</tr>
												<?php endforeach; ?>
												</tbody>
											</table>
											<!-- /.table -->
										</div>
										<!-- /.mail-box-messages -->
									<?php endif; ?>
								</div>
								<!-- /.tab-pane -->
								<div class="tab-pane" id="unpublished">
									<?php if( empty($documents['unpublished']) ): ?>
										<p class="text-center padding-10"><?php echo lang('folder_empty'); ?></p>
									<?php else: ?>
										<div class="table-responsive mailbox-messages">
											<table class="table table-hover table-striped search" style="width:100%">
												<thead>
													<tr>
														<th><?php echo lang('documents_document'); ?></th>
														<th><?php echo lang('documents_category'); ?></th>
														<th><?php echo lang('folder_folder'); ?></th>
														<th><?php echo lang('documents_owner'); ?></th>
														<th><?php echo lang('documents_last_revised'); ?></th>
													</tr>
												</thead>
												<tbody>
												<?php foreach($documents['unpublished'] as $document): ?>
													<tr>
														<td data-order="<?php echo $document->name; ?>" data-search="<?php echo $document->name; ?>">
														<?php 
														$file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
														($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
														($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
														echo $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->name); ?></td>
														<td><?php if ($document->document_category && isset($documents_category[$document->document_category])): ?>
															<div class="category-tag"><?php echo $documents_category[$document->document_category]; ?></div>
															<?php endif; ?>
														</td>
														<td><?php echo safe_anchor('perspective', array($document->menu_id, $document->folder_id), $document->folder_name); ?></td>
														<td><?php echo username($document->owner); ?></td>
														<td><?php echo $document->last_revised; ?></td>
													</tr>
												<?php endforeach; ?>
												</tbody>
											</table>
											<!-- /.table -->
										</div>
										<!-- /.mail-box-messages -->
									<?php endif; ?>
								</div>
								<!-- /.tab-pane -->
								<div class="tab-pane" id="draft">
									<?php if( empty($documents['draft']) ): ?>
										<p class="text-center padding-10"><?php echo lang('folder_empty'); ?></p>
									<?php else: ?>
										<div class="table-responsive mailbox-messages">
											<table class="table table-hover table-striped search" style="width:100%">
												<thead>
													<tr>
														<th><?php echo lang('documents_document'); ?></th>
														<th><?php echo lang('documents_category'); ?></th>
														<th><?php echo lang('folder_folder'); ?></th>
														<th><?php echo lang('documents_owner'); ?></th>
														<th><?php echo lang('documents_edited_date'); ?></th>
														<th data-orderable="false"></th>
													</tr>
												</thead>
												<tbody>
												<?php foreach($documents['draft'] as $document): ?>
													<tr>
														<td data-order="<?php echo $document->name; ?>" data-search="<?php echo $document->name; ?>">
														<?php 
														$file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
														($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
														($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
														echo $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->name); ?></td>
														<td><?php if ($document->document_category): ?>
															<div class="category-tag"><?php echo $documents_category[$document->document_category]; ?></div>
															<?php endif; ?>
														</td>
														<td><?php echo safe_anchor('perspective', array($document->menu_id, $document->folder_id), $document->folder_name); ?></td>
														<td><?php echo username($document->owner); ?></td>
														<td><?php echo substr($document->edited_date, 0, 10); ?></td>
														<td>
															<div class="table-options" style="min-width: 75px;">
																<?php
																echo icon_anchor('documents/update', $document->parent_id ? $document->parent_id : $document->document_id,'<i class="fa fa-pencil" aria-hidden="true"></i>',
																	array(
																	'title' => lang('edit'),
																	'class' => 'btn btn-default',
																	)
																);
																echo form_button(array(
																	'title' => lang('documents_draft_archive'),
																	'class' => 'btn btn-primary dialog-confirm',
																	'data-id' => $document->document_id,
																	'data-dialog' => '#confirm-dialog',
																	'data-url' => base_url('documents/delete_draft'),
																	'content' => '<i class="fa fa-trash" aria-hidden="true"></i> '
																	)
																);
																?>
															</div>
														</td>
													</tr>
												<?php endforeach; ?>
												</tbody>
											</table>
											<!-- /.table -->
										</div>
										<!-- /.mail-box-messages -->
									<?php endif; ?>
								</div>
								<!-- /.tab-pane -->
								<div class="tab-pane" id="waiting-approval">
									<?php if( empty($documents['waiting-approval']) ): ?>
										<p class="text-center padding-10"><?php echo lang('folder_empty'); ?></p>
									<?php else: ?>
										<div class="table-responsive mailbox-messages">
											<table class="table table-hover table-striped no-data-table approval-table" style="width:100%">
												<thead>
													<tr>
														<th></th>
														<th><?php echo lang('documents_document'); ?></th>
														<th><?php echo lang('documents_category'); ?></th>
														<th><?php echo lang('folder_folder'); ?></th>
														<th><?php echo lang('documents_valid_until'); ?></th>
														<th><?php echo lang('documents_accepted_by'); ?></th>
														<th><?php echo lang('documents_approval_status'); ?></th>
														<th data-orderable="false"><a id="multi-approve-btn" style="display: none;" class="btn btn-success"
														onclick="approveSelected()" title="<?php echo lang('documents_accept')?>">
															<i class="fa fa-check" aria-hidden="true"></i></a>
														</th>
													</tr>
												</thead>
												<tbody>
												<?php foreach($documents['waiting-approval'] as $document): 
													// if we want the approver not to see rejected or denied documents, uncomment below
													// if ($document->owner !== $this->auth_user_id && $document->is_denied) continue;
												?>
													<tr id="<?php echo $document->document_id?>">
														<td></td>
														<td data-order="<?php echo $document->name; ?>" data-search="<?php echo $document->name; ?>">
														<?php 
														$file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
														($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
														($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
														echo $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->name); ?></td>
														<td><?php if ($document->document_category): ?>
															<div class="category-tag"><?php echo $documents_category[$document->document_category]; ?></div>
															<?php endif; ?>
														</td>
														<td><?php echo safe_anchor('perspective', array($document->menu_id, $document->folder_id), $document->folder_name); ?></td>
														<td data-order="<?php echo $document->valid_until; ?>" data-search="<?php echo $document->valid_until; ?>"><?php 
														$one_month = new DateInterval('P1M');
														$one_month_forward = new DateTime();
														$one_month_forward->add($one_month);
														$valid_until = date_create($document->valid_until);
														$flag = (new DateTime() > $valid_until ? "<img src='/resources/img/icons/error.svg' style='margin-left: -5px;
														margin-top: -4px;'/>" : 
														($one_month_forward > $valid_until ? 
														"<i class='fa fa-exclamation-triangle' style='color: #f39c12; margin-right: 5px;'></i>" : 
														"<i class='fa fa-info-circle' style='color: #00a65a; margin-right: 6px;'></i>"));
														echo $document->valid_until === '0000-00-00' ? '' : 
															($flag . ' ' . $document->valid_until) ; ?></td>
														<td><?php echo username($document->menu_owner); ?></td>
														<td><?php echo $document->is_denied ? '<div class="category-tag denied-tag" data-toggle="tooltip" title="' . $document->is_denied . 
															'">'. lang('documents_require_action') .'</div>' : 
															'<div class="category-tag waiting-approval-tag">'. lang('documents_waiting_approval') .'</div>'; ?></td>
														<td>
															<div class="table-options">
																<?php
																echo icon_anchor('documents/update', $document->parent_id ? $document->parent_id : $document->document_id,'<i class="fa fa-pencil" aria-hidden="true"></i>',
																	array(
																	'title' => lang('edit'),
																	'class' => 'btn btn-default',
																	)
																);
																?>
															</div>
														</td>
													</tr>
												<?php endforeach; ?>
												</tbody>
											</table>
											<!-- /.table -->
										</div>
										<!-- /.mail-box-messages -->
									<?php endif; ?>
								</div>
								<!-- /.tab-pane -->
							</div>
						<!-- /.tab-content -->
						</div>
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
			</section>
			<div id="confirm-dialog" style="display:none" title="<?php echo lang('documents_draft_archive'); ?>">
				<?php echo nl2br(sprintf(lang('documents_draft_archive_help'),$this->auth_name)); ?>
			</div>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>