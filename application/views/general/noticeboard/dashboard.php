<script type="text/javascript">
	window.addEventListener("load", function (){
		let hashLocation = window.location.hash.substr(1);
		if (!hashLocation) {
			hashLocation = "documents";
		}
		$("#"+hashLocation).addClass("active");
		$("#"+hashLocation+"-head").addClass("active");
		$('[data-toggle="tooltip"]').tooltip();
	}, false)
	function changeTab(hashLocation) {
		if(history.pushState) {
			history.pushState(null, null, '#' + hashLocation);
		}
		else {
			location.hash = '#' + hashLocation;
		}
		$(".tabs-head.active").removeClass("active");
		$(".tab-pane.active").removeClass("active");
		$("#"+hashLocation).addClass("active");
		$("#"+hashLocation+"-head").addClass("active");
    DataTable.tables({ visible: true , api: true}).columns.adjust();
	}
</script>
<style>
  .box-header {padding-top:25px}
</style>
<div style="font-size: 22px; margin-top: 5px; margin-left: 5px;">Att göra lista</div>
<div class="col-md-12 home-section" style="margin-top: 15px; margin-bottom: 15px; background-color: white;">
  <div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
      <li class="tabs-head" id="documents-head"><a onclick="changeTab('documents')" data-toggle="tab" aria-expanded="false"><?php echo lang('document_module'); ?></a></li>
      <li class="tabs-head" id="checklists-head"><a onclick="changeTab('checklists')" data-toggle="tab" aria-expanded="true"><?php echo lang('checklist_module'); ?></a></li>
      <li class="tabs-head" id="deviations-head"><a onclick="changeTab('deviations')" data-toggle="tab" aria-expanded="true"><?php echo lang('deviation_module'); ?></a></li>
      <!-- <li class="tabs-head" id="autosave-head"><a onclick="changeTab('autosave')" data-toggle="tab" aria-expanded="true"><?php echo lang('document_module'); ?></a></li> -->
      <li class="tabs-head" id="education-head"><a onclick="changeTab('education')" data-toggle="tab" aria-expanded="true"><?php echo lang('education_education'); ?></a></li>
      <li class="tabs-head" id="eventanalysis-head"><a onclick="changeTab('eventanalysis')" data-toggle="tab" aria-expanded="true"><?php echo lang('eventanalysis'); ?></a></li>
      <!-- <li class="tabs-head" id="tasks-head"><a onclick="changeTab('tasks')" data-toggle="tab" aria-expanded="true"><?php echo lang('tasks_tasks'); ?></a></li> -->
    </ul>
    <div class="tab-content no-padding">
      <div class="tab-pane" id="documents">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title"><?php echo lang('updated_docs_header'); ?></h3>
          </div>
          <!-- /.box-header -->
          <div id="document-list-view">
            <div class="box-body no-padding text-center" style="flex-grow: 1">
              <?php if( empty($documents) ): ?>
                <div>
									<img src="/resources/img/calendar_1.svg"/>
								</div>
								<p style="font-weight: 500; font-size: 24px"><?php echo lang('all_set'); ?></p>
              <?php else: ?>
                <div class="table-responsive mailbox-messages">
                  <table class="table table-hover table-striped search" style="width:100%">
                    <thead>
                      <tr>
                        <th><?php echo lang('documents_name'); ?></th>
                        <th><?php echo lang('documents_type'); ?></th>
                        <th><?php echo lang('documents_category'); ?></th>
                        <th><?php echo lang('documents_owner'); ?></th>
                        <th><?php echo lang('documents_edited_date'); ?></th>
                        <th><?php echo lang('documents_valid_until'); ?></th>
                        <th><?php echo lang('documents_read'); ?></th>
                      </tr>
                    </thead>
                    <tbody>
                    <?php foreach($documents as $document): ?>
                      <tr>
                        <td data-order="<?php echo $document->name; ?>" data-search="<?php echo $document->name; ?>">
                        <?php 
                        $file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
                        ($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
                        ($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
                        echo $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->name); ?></td>
                        <td><div class="category-tag"><?php echo $documents_type[$document->document_type]; ?></div></td>
                        <td><?php if (isset($document->document_category) && array_key_exists($document->document_category, $documents_category)): ?>
                          <div class="category-tag"><?php echo $documents_category[$document->document_category]; ?></div>
                          <?php endif; ?>
                        </td>
                        <td><?php echo username($document->owner); ?></td>
                        <td><?php echo substr($document->edited_date, 0, 10); ?></td>
                        <?php if( ! in_array($document->document_type,[1,2]) ): ?>
                        <td data-order="<?php echo $document->valid_until; ?>" data-search="<?php echo $document->valid_until; ?>"><?php 
                          $one_month = new DateInterval('P1M');
                          $one_month_forward = new DateTime();
                          $one_month_forward->add($one_month);
                          $valid_until = date_create($document->valid_until);
                          $flag = (new DateTime() > $valid_until ? "<img src='/resources/img/icons/error.svg' style='margin-left: -5px;
                          margin-top: -4px;'/>" : 
                          ($one_month_forward > $valid_until ? 
                          "<i class='fa fa-exclamation-triangle' style='color: #f39c12; margin-right: 5px;'></i>" : 
                          "<i class='fa fa-info-circle' style='color: #00a65a; margin-right: 6px;'></i>"));
                          echo $document->valid_until === '0000-00-00' ? '' : 
                            ($flag . ' ' . $document->valid_until) ; ?></td>
                        <?php else: ?>
                        <td data-order="0"></td>
                        <?php endif; ?>
                        <td><?php echo $document->document_read; ?></td>
                      </tr>
                    <?php endforeach; ?>
                    </tbody>
                  </table>
                  <!-- /.table -->
                </div>
                <!-- /.mail-box-messages -->
              <?php endif; ?>
            </div>
          </div>
          <!-- /.box-body -->
        </div>
        <!-- /. box -->
      </div>
      <!-- /.tab-pane -->
      <div class="tab-pane" id="eventanalysis">
        <?php if( empty($eventanalysis) ): ?>
          <div class="box-body no-padding text-center" style="flex-grow: 1">
            <div>
              <img src="/resources/img/calendar_1.svg"/>
            </div>
            <p style="font-weight: 500; font-size: 24px"><?php echo lang('all_set'); ?></p>
          </div>
        <?php else: ?>
          <?php if ( !empty($report['messages']['eventanalysis']['critical']) )
            $this->load->view('general/noticeboard/display_eventanalysis_table', array(
              "title" => 'eventanalysis_critical_head',
              "eventanalysis" => $critical_events,
              'critical' => true
            )); ?>
          <?php if ( !empty($report['messages']['eventanalysis']['warning']) )
            $this->load->view('general/noticeboard/display_eventanalysis_table', array(
              "title" => 'eventanalysis_warning',
              "eventanalysis" => $report['messages']['eventanalysis']['warning'],
              'events' => $events,
              'critical' => false
            )); ?>
          <?php if ( !empty($report['messages']['eventanalysis_actionlist']['warning']) )
            $this->load->view('general/noticeboard/display_eventanalysis_table', array(
              "title" => 'eventanalysis_actionlist_warning',
              "eventanalysis" => $report['messages']['eventanalysis_actionlist']['warning'],
              'events' => $events,
              'critical' => false
            )); ?>
        <?php endif; ?>
      </div>
      <!-- /.tab-pane -->
      <div class="tab-pane" id="checklists">
        <?php if( empty($checklists) ): ?>
          <div class="box-body no-padding text-center" style="flex-grow: 1">
            <div>
              <img src="/resources/img/calendar_1.svg"/>
            </div>
            <p style="font-weight: 500; font-size: 24px"><?php echo lang('all_set'); ?></p>
          </div>
        <?php else: ?>
          <?php $this->load->view('general/reports/checklist_display_inner.php'); ?>
        <?php endif; ?>
      </div>
      <div class="tab-pane" id="deviations">
        <?php if( empty($report['deviation']['warning']) ): ?>
          <div class="box-body no-padding text-center" style="flex-grow: 1">
            <div>
              <img src="/resources/img/calendar_1.svg"/>
            </div>
            <p style="font-weight: 500; font-size: 24px"><?php echo lang('all_set'); ?></p>
          </div>
        <?php else: ?>
          <?php $this->load->view('general/deviation/display_table', array("status" => 'incomplete', 'title' => true)); ?>
        <?php endif; ?>
      </div>
      <!-- <div class="tab-pane" id="autosave">
      </div> -->
      <div class="tab-pane" id="education">
        <?php $this->load->view('general/reports/education_display_inner.php', array('documents' => $education_documents)); ?>
      </div>
      <!-- TODO: tasks -->
      <!-- <div class="tab-pane" id="tasks"> 
      </div> -->
    </div>
  </div>
</div>