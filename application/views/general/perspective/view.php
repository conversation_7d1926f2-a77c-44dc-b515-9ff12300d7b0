<script type="text/javascript">
	window.addEventListener("load", function (){

		$(window).resize(function(e) {
			if($(window).width()<=768){
				$("#sidebar-wrapper").hide();
			}
		});
	}, false)

	function showInfo(info) {
		$("#sidebar-wrapper").html(info);
		$("#sidebar-wrapper").show().addClass('show');
		$(".content-wrapper").addClass('sidebar-right-open');
	}

	function closeInfo(){
		$("#sidebar-wrapper").removeClass('show');
		$(".content-wrapper").removeClass('sidebar-right-open');
		// Hide after animation completes
		setTimeout(function() {
			$("#sidebar-wrapper").hide();
		}, 300);
	}

	function submitTemplate(template, name) {
		$.ajax({
			url: "<?php echo base_url('documents/upload'); ?>",
			method: 'POST',
			type: 'POST',
			data: {
					kvalprakcsrf: $('meta[name="kvalprakcsrf"]').attr('content'),
					uuid_kvalprak: "new",
					folder_id: '<?php echo isset($folder_id) ? $folder_id : ''; ?>',
					template,
					name
				},
			headers: {'X-Requested-With': 'XMLHttpRequest'}
		}).then(response => {
			window.location = "/documents/update/"+response.document_id+"?first=true";
		});
	}
	function toggleDocs() {
		$(".draft").toggle();
	}
</script>
<style type="text/css">
	tr.draft td:first-child > :first-child {
		margin-left: 10px;
	}
</style>
<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right" style="display: flex">
				<a href="#" onclick="toggleDocs()" title="<?php echo lang('documents_autosave').'/'.lang('users_documents_draft') ?>" 
				class="btn btn-primary" style="margin-right: 5px; max-height: 35px;" data-toggle="button" aria-pressed="false"> 
				<?php echo lang('documents_autosave').'/'.lang('users_documents_draft') ?></a>
				<?php
				if(
					isset($menu['current']->owner) &&
					! empty($folder) &&
					(
						// $this->config->item('document_author') true or false
						is_role('Systemadministratör') OR
						acl_object_permits('menu.author',[$menu['current']->menu_id]) OR
						acl_object_permits('menu.author',[$folder_id]) OR
						$menu['current']->owner === $this->auth_user_id
						OR (
							acl_group_permits('menu.create',$menu_groups) &&
							acl_group_permits('menu.create',$folder_group,TRUE)
						) OR
						acl_object_permits('menu.create',[$menu['current']->menu_id])
					)
				)
				{
					if (!CI_ONLY_OFFICE)
						echo icon_anchor('documents/create', $folder_id, '<i class="fa fa-plus" aria-hidden="true" style="margin-right: 5px"></i>' . lang('new'),
							array(
							'title' => lang('documents_create'),
							'class' => 'btn btn-primary',
							)
						);
					else
					echo '
					<div class="dropdown" title="'.lang('documents_create').'">
						<div href="#" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
							<i class="fa fa-plus" aria-hidden="true" style="margin-right: 5px"></i>
							'.lang('new').'
							<i class="fa fa-chevron-down" aria-hidden="true" style="margin-left: 5px"></i>
						</div>
						<ul class="dropdown-menu app-box new-doc-menu">
							<li> <a href="/documents/create/'.$folder_id.'?createExt=docx"><img class="dropdown-img" src="/resources/css/images/file_docx.svg"> Dokument </a> </li>
							<li> <a href="/documents/create/'.$folder_id.'?createExt=xlsx"><img class="dropdown-img" src="/resources/css/images/file_xlsx.svg"> Kalkylblad </a> </li>
							<li> <a href="/documents/create/'.$folder_id.'?createExt=pptx"><img class="dropdown-img" src="/resources/css/images/file_pptx.svg"> Presentation </a> </li>
							<li> <a href="#" data-toggle="modal" data-target="#uploadModal"><i class="fa fa-upload" style="color: #8242AC; width: 26px; height: 28px; text-align: center;"></i> Ladda upp </a> </li>
							<li> <a href="#" data-toggle="modal" data-target="#templateModal"><i class="fa fa-book" style="color: #8242AC; width: 26px; height: 28px; text-align: center;"></i> Maller </a> </li>
						</ul>
						</div>
					';
				}
				if(
					// $this->config->item('document_author') true or false
					is_role('Systemadministratör')
					OR (
						acl_group_permits('menu.folder',$menu_groups) OR
						acl_object_permits('menu.folder',[$menu['current']->menu_id])
					)
				)
				{
					echo icon_anchor('folder', $menu['current']->menu_id, lang('edit') . ' ' . lang('menus_folders'),
						array(
						'title' => lang('edit') . ' ' . lang('menus_folders'),
						'class' => 'btn btn-primary',
						'style' => 'margin-left: 5px; max-height: 35px;'
						)
					);
				}
				// echo anchor('', '<i class="fa fa-home" aria-hidden="true"></i>',
				// 	array(
				// 	'title' => lang('home'),
				// 	'class' => 'btn btn-default'
				// 	)
				// );
				?>
			</div>
			<h1>
				<?php echo $menu['current']->name; ?>
				<small></small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<?php if( empty($folder) ): ?>
				<div class="alert alert-warning no-print"><?php echo lang('folder_create'); ?></div>
			<?php endif; ?>
			<?php if( !isset($menu['current']->owner) ): ?>
				<div class="alert alert-warning no-print"><?php echo lang('menus_owner_empty'); ?></div>
			<?php endif; ?>
			<div class="row">
				<!-- /.col -->
				<div class="col-md-12">
					<?php if( !empty($folder) ): ?>
						<div class="box box-primary">
						<div class="box-header">
							<h3 class="box-title"><?php echo $folder->name; ?></h3>
						</div>
						<!-- /.box-header -->
						<div id="document-list-view">
						<div class="box-body no-padding text-center" style="flex-grow: 1">
							<?php if( empty($documents) ): ?>
								<div style="margin: 50px 0 30px 0;">
									<img src="/resources/img/empty_folder.svg"/>
								</div>
								<p style="font-weight: 500; font-size: 24px"><?php echo lang('folder_empty'); ?></p>
								<!-- <div style="margin: 20px"><button class="btn btn-primary"> Create Document </button></div> -->
							<?php else: ?>
								<div class="mailbox-messages">
									<table class="table table-hover table-striped search">
									<thead>
										<tr>
											<th><?php echo lang('documents_document'); ?></th>
											<!-- <th><?php echo lang('documents_type'); ?></th> -->
											<th><?php echo lang('documents_category'); ?></th>
											<th><?php echo lang('documents_edited_by'); ?></th>
											<th><?php echo lang('documents_edited_date'); ?></th>
											<th><?php echo lang('documents_read'); ?></th>
											<th data-orderable="false"></th>
										</tr>
									</thead>
									<tbody>
									<?php foreach($documents as $document): ?>
										<tr class="<?php echo ($document->status == 'waiting-approval' || $document->parent_id != null || $document->status == 'draft') ? 'draft' : ''; ?>">
											<td data-order="<?php echo $document->name . (($document->status == 'waiting-approval' || $document->parent_id != null || $document->status == 'draft') ? 'd' : 'a'); ?>"
											data-search="<?php echo $document->name; ?>">
												<?php 
												$file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
												($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
												($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
												$prefix = 
												$document->status == 'waiting-approval' ?
												'<i class="fa fa-floppy-o" title="'. lang('users_documents_waiting_approval') . '" style="color: green; margin-right: 3px" aria-hidden="true"></i>' : 
												($document->parent_id != null ? '<i class="fa fa-floppy-o" title="'. lang('users_documents_draft') . '" style="margin-right: 3px" aria-hidden="true"></i>' : 
												($document->status == 'draft' ? '<i class="fa fa-floppy-o"  title="'. lang('documents_autosave') . '" style="margin-right: 5px" aria-hidden="true"></i>' : ''));
												echo $prefix . $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->name ? $document->name : substr($document->document_id, 0, 6) . '...') . 
												($document->document_attachment_count > 0 ? ' <i class="fa fa-paperclip" aria-hidden="true"></i>' : ''); ?></td>
											<!-- <td><div class="category-tag"><?php echo $documents_type[$document->document_type]; ?></div></td> -->
											<td><?php if ($document->document_category && array_key_exists($document->document_category, $documents_category)): ?>
													<div class="category-tag"><?php echo $documents_category[$document->document_category]; ?></div>
													<?php endif; ?>
												</td>
											<td><?php echo username($document->edited_by); ?></td>
											<td><?php echo substr($document->edited_date, 0, 10); ?></td>
											<td><?php echo $document->document_read; ?></td>
											<td><img style="cursor: pointer" onClick="showInfo(`
											<div>
												<div class='document-info-box-title'><?php echo $document->name; ?>
													<div style='float: right' onClick='closeInfo()'><i class='fa fa-times' aria-hidden='true'></i></div>
												</div>
												<dl class='description-list'>
													<dt><?php echo lang('documents_path'); ?></dt>
													<dd><?php echo $menu['current']->name; ?> > <?php echo $folder->name; ?></dd>
													<dt><?php echo lang('documents_type'); ?></dt>
													<dd><?php echo $documents_type[$document->document_type]; ?></dd>
													<dt><?php echo lang('documents_category'); ?></dt>
													<dd><?php echo array_key_exists($document->document_category, $documents_category) ? $documents_category[$document->document_category] : ''; ?></dd>
													<dt><?php echo lang('documents_created_by'); ?></dt>
													<dd><?php echo username($document->created_by); ?>, <?php echo substr($document->created_date, 0, 10); ?></dd>
													<?php if($document->valid_until !== '0000-00-00'): ?>
													<dt><?php echo lang('documents_valid_until'); ?></dt>
													<dd><?php 
													$one_month = new DateInterval('P1M');
													$one_month_forward = new DateTime();
													$one_month_forward->add($one_month);
													$valid_until = date_create($document->valid_until);
													$flag = (new DateTime() > $valid_until ? "<img src='/resources/img/icons/error.svg' style='margin-left: -5px;
													margin-top: -4px;'/>" : 
													($one_month_forward > $valid_until ? 
													"<i class='fa fa-exclamation-triangle' style='color: #f39c12; margin-right: 5px;'></i>" : 
													"<i class='fa fa-info-circle' style='color: #00a65a; margin-right: 6px;'></i>"));
													echo $document->valid_until === '0000-00-00' ? '' : 
														($flag . ' ' . $document->valid_until) ;
													?></dd>
													<?php endif; ?>
													<dt><?php echo lang('documents_owner'); ?></dt>
													<dd><?php echo username($document->owner); ?></dd>
													<dt><?php echo lang('documents_created'); ?></dt>
													<dd><?php echo $document->created; ?></dd>
													<?php if( $document->edited_date !== '0000-00-00 00:00:00' ): ?>
														<dt><?php echo lang('documents_edited_by'); ?></dt>
														<dd><?php echo username($document->edited_by); ?>, <?php echo $document->edited_date; ?></dd>
													<?php endif; ?>
												</dl>
											</div>`)" 
											src="/resources/img/icons/menu-fold.svg" /></td>
										</tr>
									<?php endforeach; ?>
									</tbody>
									</table>
									<!-- /.table -->
								</div>
								<!-- /.mail-box-messages -->
							<?php endif; ?>
						</div>
						</div>
						<!-- /.box-body -->
						</div>
						<!-- /. box -->
					<?php endif; ?>
				</div>

				<!-- /.col -->
			</div>
			<!-- /.row -->
			<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog" aria-labelledby="uploadModalLabel" aria-hidden="true">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="uploadModalLabel">Ladda upp dokument</h4>
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						</div>
						<div class="ml-4">
								Ladda upp editerbara dokument, accepterade format .docx, .xlsx, pptx. 
								<br>
								PDF läggs som bilagor i existerande dokument 
							</div>
						<div class="modal-body">
							<div id="dropzone" class="form-control dropzone"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
						</div>
					</div>
				</div>
			</div>

			<div class="modal fade" id="templateModal" tabindex="-1" role="dialog" aria-labelledby="templateModalLabel" aria-hidden="true">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="templateModalLabel"><?php echo lang('templates'); ?></h4>
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						</div>
						<div class="modal-body">
							<h5 class="modal-title"><?php echo lang('templates'); ?></h5>
							<?php foreach ($templates as $key => $template): ?>
								<div style="margin: 10px 0;"> 
									<a href="#" onclick="submitTemplate('<?php echo $template['file']; ?>', '<?php echo $template['name']; ?>')">
										<img style="vertical-align: bottom; margin-right: 5px" src="/resources/css/images/file_<?php echo substr($template['file'], -4); ?>.svg"> 
										<?php echo $template['name']; ?> 
									</a> 
								</div>
							<?php endforeach; ?>
							<h5 class="modal-title"><?php echo lang('generic_templates'); ?></h5>
							<div style="margin: 10px 0;"> 
								<a href="#" onclick="submitTemplate('orna/Laglista Orna Q4 2024 B.xlsx', 'Laglista Orna Q4 2024 B')">
									<img style="vertical-align: bottom; margin-right: 5px" src="/resources/css/images/file_xlsx.svg"> Laglista Orna Q4 2024 B </a> 
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->

	<!-- Right Sidebar for Document Info -->
	<div id="sidebar-wrapper" style="display:none">
	</div>
	<!-- /.sidebar-wrapper -->

<?php $this->load->view('template/footer-perspective'); ?>