<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<div class="float-right btn-group">
					<?php
					if( ! empty($rights['create']) )
					{
						echo anchor('riskassessments/add','<i class="fa fa-plus" aria-hidden="true"></i>',
							array(
							'title' => 'Skapa en riskanalys',
							'class' => 'btn btn-primary',
							)
						);
						// @TODO: Print
						echo form_button('','<i class="fa fa-print" aria-hidden="true"></i>',
							array(
							'title' => 'Skriv ut',
							'class' => 'btn btn-default',
							'onclick' => 'window.print()'
							)
						);
					}
					echo anchor('', '<i class="fa fa-home" aria-hidden="true"></i>',
						array(
						'title' => lang('home'),
						'class' => 'btn btn-default'
						)
					);
					?>
				</div>
				<h1>Riskbedömning
					<small></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<section class="content">
				<div class="no-print">
					<div class="callout callout-info" style="color: #172B4D !important;">
						Lagen kräver att man vid någon förändring av verksamheten (ny specialitet, ny teknisk
						utrustning, nya metoder och processer) skall utföra en riskbedömning där man listar de
						risker som kan uppstå, anger hur allvarlig varje risk är samt hur frekvent man bedömer att
						risken kan inträffa.
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header with-border">
								<h3 class="box-title">
									Riskbedömning
								</h3>
							</div>
							<div class="box-body no-padding">
								<?php
								if( ! empty($rights['read']) ):
									if(!empty($risks)):
								?>
									<table class="table table-striped no-data-table dated-table">
										<thead>
											<tr>
												<th width="25%">Rubrik</th>
												<th width="10%">Skapades</th>
												<th>Ärendebeskrivning</th>
												<th width="85px">&nbsp;</th>
											</tr>
										</thead>
										<tbody>
											<?php
											foreach($risks as $risk):
												if(strlen($risk->description)>72) {
													$risk->description = substr($risk->description,0,72).'...';
												}
												$risk->name?$risk->name:'Ingen rubrik vald'
											?>
											<tr>
												<td><?php echo safe_anchor('riskassessments/view',$risk->ra_id,$risk->name); ?></td>
												<td><?= $risk->date?$risk->date:''; ?></td>
												<td><?= $risk->description; ?></td>
												<?php if( ! empty($rights['update']) ) { ?>
												<td>
													<div class="btn-group btn-group-sm">
														<?php
														echo icon_anchor('riskassessments/edit',$risk->ra_id,'<i class="fa fa-pencil" aria-hidden="true"></i>',
															array(
															'title' => 'Redigera',
															'class' => 'btn btn-default',
															)
														);
														
														echo icon_anchor('riskassessments/connect',$risk->ra_id,'<i class="fa fa-link" aria-hidden="true"></i>',
															array(
															'title' => 'Anslut',
															'class' => 'btn btn-default',
															)
														);
														?>
													</div>
												</td>
												<?php } else { ?>
												<td></td>
												<?php } ?>
											</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
									<?php else: ?>
										<p class="margin">Du har inte skapat några riskanalyser än.</p>
									<?php endif; ?>
								<?php else: ?>
									<p class="margin">Du har inte rättigheter att läsa riskanalyser.</p>
								<?php endif; ?>
							</div>
						</div>
					</div>
					<!-- /.col-md-12-->
				</div>
				<!-- /.row -->
			</section>
			<!-- /section -->
		</div>
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');