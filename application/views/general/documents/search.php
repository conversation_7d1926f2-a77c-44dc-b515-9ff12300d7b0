<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<h1>
					<?php echo lang('search'); ?>
					<small></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<?php // var_dump($sidebar['menu']['structure']); ?>
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<?php
						echo form_open(NULL,array(
							'id' => 'form-company-group',
							'method' => 'get',
							'autocomplete' => 'off'
						));
						?>
						<div class="box box-solid">
							<div class="box-header">
								<?php
								echo form_input(array(
										'name'	=> 's',
										'value'	=> set_value('search'),
										'class' => 'form-control',
										'placeholder' => lang('search_placeholder')
									));
								?>
							</div>
						</div>
						<?php echo form_close(); ?>
					</div>
				</div>
				<?php if( !empty($documents) ): ?>
					<div class="row">
						<div class="col-md-12">
							<div class="box">
								<div class="box-header">
									<h3 class="box-title"><?php echo lang('documents_document'); ?></h3>
								</div>
								<!-- /.box-header -->
								<div class="box-body no-padding">
									<?php if( empty($documents) ): ?>
										<p class="text-center"><?php echo lang('no_results'); ?></p>
									<?php else: ?>
										<div class="table-responsive mailbox-messages">
											<table class="table table-hover table-striped no-data-table">
											<tbody>
												<colgroup>
													<col style="width: 30px;">
													<col style="width: 250px;">
													<col>
													<col style="width: 100px">
												</colgroup>
											<?php foreach($documents as $document): ?>
												<tr>
													<td class="mailbox-star"><a href="#"><i class="fa fa-circle-o text-light-blue"></i></a></td>
													<td class="mailbox-name"><?php echo safe_anchor('documents', $document->document_id, $document->document_name); ?></td>
													<td class="mailbox-subject"><?php echo $document->document_description; ?>
													</td>
													<td class="mailbox-date"><?php echo $document->document_last_revised; ?></td>
												</tr>
											<?php endforeach; ?>
											</tbody>
											</table>
											<!-- /.table -->
										</div>
										<!-- /.mail-box-messages -->
									<?php endif; ?>
								</div>
								<!-- /.box-body -->
							</div>
							<!-- /. box -->
						</div>
						<!-- /.col -->
					</div>
					<!-- /.row -->
				<?php endif; ?>
			</section>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>