<div class="box box-solid">
  <div class="box-header with-border">
    <?php if (isset($title) && $title == true): ?>
      <h3 class="box-title"><?php echo lang('deviations_tab_header'); ?></h3>
    <?php endif; ?>
  </div>
  <div class="box-body no-padding">
    <?php
    if( ! empty($rights['read']) ):
      if( ! isset($results) || $results === FALSE ):
        echo '<p class="margin">Inga avvikelser matchade dina sökparametrar.</p>';
      endif;
      if(!empty($deviations) && isset($results) && $results === TRUE ):
    ?>
      <table class="deviation-table table table-striped search no-data-table" style="width:100%">
        <thead>
          <tr>
            <th>Skapat datum</th>
            <th>Rubrik</th>
            <?php if ($status == 'incomplete'): ?>
              <th width="10%">Steg</th>
            <?php endif; ?>
            <?php foreach($list as $key => $val): 
              $field = $fields['all'][$key]; 
              if ($field->input == 'input' && $field->required_kvalprak == 1) {
                continue;
              }		
            ?>
              <th><?php echo $val ?></th>
            <?php endforeach; ?>
            <th width="85px" class="no-print">&nbsp;</th>
          </tr>
        </thead>
        <tbody>
          <?php
          $heading_df_id = 0;
          foreach($list as $df_id => $val): 
            $field = $fields['all'][$df_id]; 
            if ($field->input == 'input' && $field->required_kvalprak == 1) {
              $heading_df_id = $df_id;
              break;
            }		
          endforeach;
          foreach($deviations as $id => $deviation):
            $deviation_df = array_keys($deviation);
            if (!array_key_exists($heading_df_id, $deviation)) continue;
            $value = $deviation[$heading_df_id];
            if ($value->status != $status) continue;
          ?>
          <tr>
            <td style="min-width: 75px;"> <?php echo substr($value->reg_date_one, 0, 10) ?></td>
            <?php if ($status == 'incomplete'): ?>
              <td style="max-width: 350px"><?php echo safe_anchor('deviation/add/' . (isset($value->reg_date_two) ? '3' : '2'), $id,$value->answer); ?>
                <?php echo ($value->deviation_attachment_count > 0 ? ' <i class="fa fa-paperclip" aria-hidden="true"></i>' : ''); ?>
              </td>
              <td><?php echo isset($value->reg_date_two) ? '<div class="stage-3-tag">'.lang('stage2_started').'</div>' : '<div class="stage-2-tag">'.lang('stage1_reported').'</div>'; ?> </td>
            <?php else: ?>
              <td style="max-width: 350px"><?php echo safe_anchor('deviation/view',$id,$value->answer); ?>
                <?php echo ($value->deviation_attachment_count > 0 ? ' <i class="fa fa-paperclip" aria-hidden="true"></i>' : ''); ?>
              </td>
            <?php endif; ?>
            
            <?php
            foreach($list as $df_id => $l):
              $field = $fields['all'][$df_id]; 
              if ($field->input == 'input' && $field->required_kvalprak == 1) {
                continue;
              }		
              if( ! in_array($df_id, $deviation_df) )
              {
                echo '<td></td>';
                continue;
              }

              $d = $deviation[$df_id];
              $o = isset($options[$d->df_id]) ? $options[$d->df_id] : array();
              echo '<td style="max-width: 200px">' . forms_value($d->input,$o,$d->answer) . '</td>';
            endforeach;
            ?>
            <?php
            if( ! empty($rights['update']) ) { ?>
            <td class="no-print">
              <div class="btn-group btn-group-sm" style="min-width: 130px">
                <?php
                if ($status == 'active') {
                  echo icon_anchor('deviation/mark_incomplete',$id,'<i class="fa fa-undo" aria-hidden="true"></i>',
                  array(
                  'title' => lang('deviation_mark_incomplete'),
                  'class' => 'btn btn-default',
                  ));
                  echo icon_anchor('deviation/edit/1',$id,'<i class="fa fa-pencil" aria-hidden="true"></i>',
                    array(
                    'title' => 'Redigera',
                    'class' => 'btn btn-default',
                    )
                  );

                  echo icon_anchor('deviation/connect',$id,'<i class="fa fa-link" aria-hidden="true"></i>',
                    array(
                    'title' => 'Anslut',
                    'class' => 'btn btn-default',
                    )
                  );
                  echo icon_anchor('deviation/resolve',$id,'<i class="fa fa-check" aria-hidden="true"></i>',
                    array(
                    'title' => lang('deviation_resolve'),
                    'class' => 'btn btn-default',
                    ));
                } else if  ($status == 'resolved') {
                  echo icon_anchor('deviation/activate',$id,'<i class="fa fa-undo" aria-hidden="true"></i>',
                  array(
                  'title' => lang('deviation_unresolve'),
                  'class' => 'btn btn-default',
                  ));
                  echo icon_anchor('deviation/archive',$id,'<i class="fa fa-trash" aria-hidden="true"></i>',
                  array(
                  'title' => lang('deviation_archive'),
                  'class' => 'btn btn-default',
                  ));
                } else if  ($status == 'archived') {
                  echo icon_anchor('deviation/resolve_archived',$id,'<i class="fa fa-undo" aria-hidden="true"></i>',
                  array(
                  'title' => lang('deviation_unarchive'),
                  'class' => 'btn btn-default',
                  ));
                }
                ?>
              </div>
            </td>
            <?php } else { ?>
            <td class="no-print"></td>
            <?php } ?>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
      <?php elseif( isset($results) && $results !== FALSE ): ?>
        <p class="margin">Du har inte skapat några avvikelser än.</p>
      <?php endif; ?>
    <?php else: ?>
      <p class="margin">Du har inte rättigheter att läsa avvikelser.</p>
    <?php endif; ?>
  </div>
</div>