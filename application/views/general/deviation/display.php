<script type="text/javascript">
	window.addEventListener("load", function (){
		let hashLocation = window.location.hash.substr(1);
		if (!hashLocation) {
			hashLocation = "<?php echo isset($report['deviation']['warning']) && ! empty($report['deviation']['warning']) ? "incomplete" : "active"; ?>";
		}
		$("#"+hashLocation).addClass("active");
		$("#"+hashLocation+"-head").addClass("active");
		$('[data-toggle="tooltip"]').tooltip();
	}, false)
	function changeTab(hashLocation) {
		if(history.pushState) {
			history.pushState(null, null, '#' + hashLocation);
		}
		else {
			location.hash = '#' + hashLocation;
		}
		$(".tabs-head.active").removeClass("active");
		$(".tab-pane.active").removeClass("active");
		$("#"+hashLocation).addClass("active");
		$("#"+hashLocation+"-head").addClass("active");
		DataTable.tables({ visible: true , api: true}).columns.adjust();
	}
</script>
<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				if( ! empty($rights['create']) && $newDeviation ):
					echo anchor('deviation/add/1','Skapa en avvikelserapport',
						array(
						'title' => 'Skapa en avvikelserapport',
						'class' => 'btn btn-primary',
						'style' => 'margin-right:4px'
						)
					);
				endif;
				// @TODO: Print
				if( ! empty($rights['read']) ):
					echo form_button('','<i class="fa fa-print" aria-hidden="true"></i>',
						array(
						'title' => 'Skriv ut',
						'class' => 'btn btn-default',
						'style' => 'color: inherit; height: 34px',
						'onclick' => 'window.print()'
						)
					);
				endif;
				if(CI_DEVIATION_EXPORT && is_role('Systemadministratör'))
				{
					echo form_button([
						'name' => 'export',
						'type' => 'submit',
						'form' => 'form-company-group',
						'class' => 'btn btn-default',
						'title' => lang('deviation_export'),
						'content' => '<i class="fa fa-download"></i>',
						'style' => 'color: inherit; height: 34px'
					]);		
				}
				echo anchor('', '<i class="fa fa-home" aria-hidden="true"></i>',
					array(
					'title' => lang('home'),
					'style' => 'color: inherit; height: 34px',
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>Avvikelsehantering
				<small>Avvikelser</small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<?php if( ! $newDeviation ): ?>
				<div class="alert alert-danger no-print">Du kan inte skapa några avvikelser för tillfället - alla obligatoriska fält är inte aktiverade.</div>
			<?php endif; ?>
			<?php if( empty($rights['create']) ): ?>
				<div class="alert alert-warning no-print">Du har inte rättigheter att skapa avvikelser.</div>
			<?php else: ?>
			<div class="row">
				<div class="col-md-12">
					<?php echo get_flashdata('flashdata', 'success', TRUE, FALSE); ?>

					<div class="nav-tabs-custom">
						<ul class="nav nav-tabs">
							<li class="tabs-head" id="incomplete-head"><a style="color: #DE350B;" onclick="changeTab('incomplete')" data-toggle="tab" aria-expanded="false"><?php echo lang('deviation_incomplete'); ?></a></li>
							<li class="tabs-head" id="active-head"><a style="color: #0052CC;" onclick="changeTab('active')"  data-toggle="tab" aria-expanded="true"><?php echo lang('deviation_active'); ?></a></li>
							<li class="tabs-head" id="resolved-head"><a style="color: #00875A;" onclick="changeTab('resolved')"  data-toggle="tab" aria-expanded="true"><?php echo lang('deviation_resolved'); ?></a></li>
							<li class="tabs-head" id="archived-head"><a style="color: #42526E;" onclick="changeTab('archived')"  data-toggle="tab" aria-expanded="true"><?php echo lang('deviation_archived'); ?></a></li>
						</ul>
						<div class="tab-content no-padding">
							<div class="tab-pane" id="incomplete">
								<?php $this->load->view('general/deviation/display_table', array("status" => 'incomplete')); ?>
							</div>
							<!-- /.tab-pane -->
							<div class="tab-pane" id="active">
								<?php $this->load->view('general/deviation/display_table', array("status" => 'active')); ?>
							</div>
							<!-- /.tab-pane -->
							<div class="tab-pane" id="resolved">
								<?php $this->load->view('general/deviation/display_table', array("status" => 'resolved')); ?>
							</div>
							<div class="tab-pane" id="archived">
								<?php $this->load->view('general/deviation/display_table', array("status" => 'archived')); ?>
							</div>
						</div>
       		</div>
					
				</div>
				<!-- /.col-md-9-->
			</div>
			<!-- /.row -->
			<?php endif; ?>
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>