<script src="<?php echo cdn_url('vendor/echarts/echarts.min.js'); ?>"></script>

<script type="text/javascript">
	let charts = <?php echo json_encode($charts); ?>;

	window.onload = function (){
		drawCharts();
	}

	function drawCharts() {
		charts.forEach((chart, i) => {
			let myChart = echarts.init(document.getElementById('progress-chart-' + (i+1)));
			myChart.setOption(chart, true);
			window.addEventListener('resize', myChart.resize);
		});
	}
</script>

<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				if( is_role('Systemadministratör') )
				{
					echo anchor('tasks/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
						array(
						'title' => lang('tasks_create'),
						'class' => 'btn btn-primary',
						)
					);
				}
				echo anchor('', '<i class="fa fa-home" aria-hidden="true"></i>',
					array(
					'title' => lang('home'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang("tasks_tasks"); ?>
				<small></small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<!-- /.col -->
				<div class="col-md-12">
					<!-- /.box-header -->
          <div class="box-body no-padding">
            <?php if( empty($tasks) ): ?>
              <p class="text-center"><?php echo lang('tasks_empty'); ?></p>
            <?php else: ?>
							<div class="row">
								<?php 
									$i = 1;
									foreach($charts as $chart) {
										echo '<div class="col-md-4"> <div id="progress-chart-' . $i . '" style="height: 350px; z-index:0"></div> </div>';
										$i += 1;
									}
								?>
							</div>
              <div class="table-responsive mailbox-messages">
                <table class="table table-hover table-striped">
								<thead>
									<tr>
										<th><b><?php echo lang('tasks_list_task'); ?></b></th>
										<th><b><?php echo lang('tasks_list_progress'); ?></b></th>
										<th><b><?php echo lang('tasks_list_type'); ?></b></th>
										<th><b><?php echo lang('tasks_list_description'); ?></b></th>
										<th><b><?php echo lang('tasks_list_owner'); ?></b></th>
										<th><b><?php echo lang('tasks_list_due_date'); ?></b></th>
									</tr>
								</thead>
                <tbody>
                <?php foreach($tasks as $task): ?>
                  <tr>
                    <td class="mailbox-name"><?php echo safe_anchor('tasks', $task->task_id, $task->name); ?></td>
										<td class="mailbox-date" style="color: <?php 
											$percent = $task->target == 0 ? 100 : 100 * $task->progress / $task->target;
											echo ($percent > 75) ? '#3ba272' : ( ($percent > 30) ? '#fc8452' : '#ee6666') ?>">
											<?php echo round($task->progress, 2) . '/' . $task->target; ?>
										</td>
										<td class="mailbox-date"><?php echo array_key_exists($task->task_type, $tasks_type) ? $tasks_type[$task->task_type] : ''; ?></td>
                    <td class="mailbox-subject"><?php echo $task->description; ?></td>
										<td class="mailbox-subject"><?php echo username($task->owner); ?></td>
                    <td class="mailbox-date"><?php echo $task->finish_date == "0000-00-00" ? '' : $task->finish_date; ?></td>
                  </tr>
                <?php endforeach; ?>
                </tbody>
                </table>
                <!-- /.table -->
              </div>
              <!-- /.mail-box-messages -->
            <?php endif; ?>
          </div>
          <!-- /.box-body -->
				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</section>
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>