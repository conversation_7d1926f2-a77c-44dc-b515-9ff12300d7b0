<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<div class="float-right btn-group">
					<?php
					echo anchor('', '<i class="fa fa-home" aria-hidden="true"></i>',
						array(
						'title' => lang('home'),
						'class' => 'btn btn-default'
						)
					);
					?>
				</div>
				<h1>Checklistor
					<small></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header with-border">
								<h3 class="box-title">
									Checklistor
								</h3>
							</div>
							<div class="box-body no-padding">
								<?php if( ! empty($pages) ): ?>
									<table class="table search table-striped">
										<thead>
											<tr>
												<th><?php echo lang("forms_name") ?></th>
											</tr>
										</thead>
										<tbody>
											<?php foreach($pages as $page_id => $page): ?>
												<tr>
													<td><?php echo safe_anchor('form/checklist/list', $page_id, $forms[$page->form_id]->name); ?></td>
												</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
								<?php else: ?>
									<p class="margin">Du har inte skapat några checklistor än.</p>
								<?php endif; ?>
							</div>
						</div>
					</div>
					<!-- /.col-md-12-->
				</div>
				<!-- /.row -->
			</section>
			<!-- /section -->
		</div>
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');