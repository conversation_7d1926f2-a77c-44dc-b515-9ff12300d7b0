<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<h1>
					<?php echo lang('reports'); ?>
					<small><?php echo lang('reports_' . $report['severity']); ?> > <?php echo lang('documents_document'); ?></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<?php // var_dump($sidebar['menu']['structure']); ?>
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header">
								<h3 class="box-title"><?php echo lang('report_documents_' . $report['severity']); ?></h3>
							</div>
							<!-- /.box-header -->
							<div class="box-body no-padding">
								<?php if( empty($documents) ): ?>
									<p class="text-center"><?php echo lang('folder_empty'); ?></p>
								<?php else: ?>
									<div class="table-responsive mailbox-messages">
										<table class="table table-hover table-striped">
											<thead>
												<tr>
													<th><?php echo lang('documents_name'); ?></th>
													<th><?php echo lang('documents_description'); ?></th>
													<th><?php echo lang('documents_valid_until'); ?></th>
													<th data-orderable="false"></th>
												</tr>
											</thead>
											<tbody>
											<?php foreach($documents as $document): ?>
												<tr>
													<td class="mailbox-name"><?php echo safe_anchor('documents', $document->document_id, $document->document_name); ?></td>
													<td class="mailbox-subject"><?php echo $document->document_description; ?></td>
													<td class="mailbox-date"><?php echo $document->document_valid_until; ?></td>
													<td>
														<div class="btn-group btn-group-sm">
															<?php
															echo icon_anchor('documents/update', $document->document_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
																array(
																'title' => lang('edit'),
																'class' => 'btn btn-default',
																)
															);
															?>
														</div>
													</td>
												</tr>
											<?php endforeach; ?>
											</tbody>
										</table>
										<!-- /.table -->
									</div>
									<!-- /.mail-box-messages -->
								<?php endif; ?>
							</div>
							<!-- /.box-body -->
						</div>
						<!-- /. box -->
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
			</section>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>