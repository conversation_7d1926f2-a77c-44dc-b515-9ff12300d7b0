<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<h1>
					<?php echo lang('reports'); ?>
					<small><?php echo lang('reports_' . $report['severity']); ?> > <?php echo lang('tasks'); ?></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<?php // var_dump($sidebar['menu']['structure']); ?>
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header">
								<h3 class="box-title"><?php echo lang('tasks_' . $report['severity']); ?></h3>
							</div>
							<!-- /.box-header -->
							<div class="box-body no-padding">
								<?php if( empty($tasks) ): ?>
									<p class="text-center"><?php echo lang('folder_empty'); ?></p>
								<?php else: ?>
									<div class="table-responsive mailbox-messages">
										<table class="table table-hover table-striped">
											<thead>
												<tr>
													<th>Mål</th>
													<th>Målbeskrivning</th>
												</tr>
											</thead>
											<tbody>
											<?php foreach($tasks as $id => $task): ?>
												<tr>
													<td><?php echo safe_anchor('tasks/', $id, $task->name); ?></td>
													<td><?php echo substr($task->description,0,100).'...'; ?></td>
												</tr>
											<?php endforeach; ?>
											</tbody>
										</table>
										<!-- /.table -->
									</div>
									<!-- /.mail-box-messages -->
								<?php endif; ?>
							</div>
							<!-- /.box-body -->
						</div>
						<!-- /. box -->
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
			</section>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>