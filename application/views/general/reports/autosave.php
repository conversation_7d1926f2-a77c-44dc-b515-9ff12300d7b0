<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<h1>
					<?php echo lang('reports'); ?>
					<small><?php echo lang('reports_' . $report['severity']); ?> > <?php echo lang('documents_document'); ?> - <?php echo lang('documents_autosave'); ?></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<?php // var_dump($sidebar['menu']['structure']); ?>
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header">
								<h3 class="box-title"><?php echo lang('report_autosave_' . $report['severity']); ?></h3>
							</div>
							<!-- /.box-header -->
							<div class="box-body no-padding">
								<?php if( empty($autosave) ): ?>
									<p class="text-center"><?php echo lang('folder_empty'); ?></p>
								<?php else: ?>
									<div class="mailbox-messages">
										<table class="table table-hover table-striped">
											<thead>
												<tr>
													<th><?php echo lang('documents_document'); ?></th>
													<th><?php echo lang('documents_category'); ?></th>
													<th><?php echo lang('folder_folder'); ?></th>
													<th><?php echo lang('documents_edited_date'); ?></th>
													<th data-orderable="false"></th>
												</tr>
											</thead>
											<tbody>
											<?php foreach($autosave as $document_id => $d): ?>
												<tr>
													<td data-order="<?php echo $documents[$document_id]->name ? $documents[$document_id]->name : $document_id; ?>" data-search="<?php echo $documents[$document_id]->name ? $documents[$document_id]->name : $document_id; ?>">
														<?php 
														$file_type = $documents[$document_id]->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
														($documents[$document_id]->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
														($documents[$document_id]->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
														echo $file_type . ' ' . safe_anchor($documents[$document_id]->is_only_office ? 'documents/update': 'documents/autosave/view', 
															$documents[$document_id]->document_id, $documents[$document_id]->name ? $documents[$document_id]->name : substr($document_id, 0, 6) . '...'); ?></td>
													<td><?php if ($documents[$document_id]->document_category): ?>
														<div class="category-tag"><?php echo $documents_category[$documents[$document_id]->document_category]; ?></div>
														<?php endif; ?>
													</td>
													<td><?php echo safe_anchor('perspective', array($documents[$document_id]->menu_id, $documents[$document_id]->folder_id), $documents[$document_id]->folder_name); ?></td>
													<td><?php echo substr($documents[$document_id]->edited_date, 0, 10); ?></td>
													
													<td><?php echo CI_ONLY_OFFICE ? 
													form_button(array(
														'title' => lang('documents_draft_archive'),
														'class' => 'btn btn-primary dialog-confirm',
														'data-id' => $document_id,
														'data-dialog' => '#confirm-dialog',
														'data-url' => base_url('documents/delete_draft'),
														'content' => '<i class="fa fa-trash" aria-hidden="true"></i> '
														)
													)
													: $d->comment; ?></td>
												</tr>
											<?php endforeach; ?>
											</tbody>
										</table>
										<!-- /.table -->
									</div>
									<!-- /.mail-box-messages -->
								<?php endif; ?>
							</div>
							<!-- /.box-body -->
						</div>
						<!-- /. box -->
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
			</section>
			<div id="confirm-dialog" style="display:none" title="<?php echo lang('documents_draft_archive'); ?>">
				<?php echo nl2br(sprintf(lang('documents_draft_archive_help'),$this->auth_name)); ?>
			</div>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>