<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<div class="float-right btn-group">
					<a href="javascript:window.history.go(-1);" title="<?php echo lang('back') ?>" class="btn btn-secondary"
						style="color: inherit; height: 34px"><i class="fa fa-reply" aria-hidden="true" style="line-height: 20px;"></i></a>
				</div>
				<h1>
					<?php echo lang('reports'); ?>
					<small><?php echo lang('login_logins'); ?></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header with-border">
								<h3 class="box-title"><?php echo lang('login_logins'); ?></h3>
							</div>
							<!-- /.box-header -->
							<div class="box-body no-padding">
							<div class="table-responsive mailbox-messages">
								<table class="table table-hover table-striped" style="width:100%">
									<thead>
										<tr>
											<th><?php echo lang('users_name'); ?></th>
											<th><?php echo lang('users_last_login'); ?></th>
											<th><?php echo lang('users_last_visit'); ?></th>
										</tr>
									</thead>
									<tbody>
									<?php foreach($this->users as $user): ?>
										<tr>
											<td><?php echo username($user->user_id); ?></td>
											<td><?php echo $user->last_login; ?></td>
											<td><?php echo $user->last_visit; ?></td>
										</tr>
									<?php endforeach; ?>
									</tbody>
								</table>
							</div>
							<!-- /.box-body -->
						</div>
						<!-- /. box -->
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
			</section>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>