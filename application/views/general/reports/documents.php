<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				if( is_role('Systemadministratör') ) {
					echo icon_anchor('reports/documents_overview', null, '
					<i class="fa fa-reply" aria-hidden="true" style="line-height: 20px;"></i>',
						array(
						'title' => lang('back'),
						'class' => 'btn btn-secondary',
						'style' => 'color: inherit; height: 34px'
						)
					);
				}
				?>
			</div>
			<h1><?php echo lang('reports'); ?>
				<small><?php echo lang('documents_document'); ?></small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-3 no-print search-options">
					<?php
						echo validation_errors();
						echo form_open(NULL,array(
							'id' => 'form-company-group',
							'method' => 'get',
							'autocomplete' => 'off'
						));
					?>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								Sökalternativ
							</h3>
						</div>
						<div class="box-body no-padding">
							<div class="form-group date even">
								<?php echo form_label(lang('documents_created'),'kcs'); ?>
								<?php
									echo form_input(array(
											'name'	=> 'kcs',
											'value'	=> set_value('kcs'),
											'class' => 'form-control datepicker',
											'placeholder' => lang('from')
										));
								?>
								<?php
									echo form_input(array(
											'name'	=> 'kce',
											'value'	=> set_value('kce'),
											'class' => 'form-control datepicker',
											'placeholder' => lang('to')
										));
								?>
							</div>
							<div class="form-group date even">
								<?php echo form_label(lang('documents_edited_date'),'kms'); ?>
								<?php
									echo form_input(array(
											'name'	=> 'kms',
											'value'	=> set_value('kms'),
											'class' => 'form-control datepicker',
											'placeholder' => lang('from')
										));
								?>
								<?php
									echo form_input(array(
											'name'	=> 'kme',
											'value'	=> set_value('kme'),
											'class' => 'form-control datepicker',
											'placeholder' => lang('to')
										));
								?>
							</div>
							<div class="form-group date odd">
								<?php echo form_label(lang('documents_valid_until'),'kvs'); ?>
								<?php
									echo form_input(array(
											'name'	=> 'kvs',
											'value'	=> set_value('kvs'),
											'class' => 'form-control datepicker',
											'placeholder' => lang('from')
										));
								?>
								<?php
									echo form_input(array(
											'name'	=> 'kve',
											'value'	=> set_value('kve'),
											'class' => 'form-control datepicker',
											'placeholder' => lang('to')
										));
								?>
							</div>
							<div class="form-group even">
								<?php echo form_label(lang('documents_type'),'dtype'); ?>
								<?php
									echo form_dropdown('dtype', $documents_type, set_value('dtype'),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<div class="form-group even">
								<?php echo form_label(lang('documents_category'),'dcategory'); ?>
								<?php
									echo form_dropdown('dcategory', $documents_category, set_value('dcategory'),array(
										'class' => 'form-control'
									));
								?>
							</div>

							<div class="form-group even">
								<?php echo form_label(lang('documents_owner'),'owner'); ?>
								<?php
									echo form_dropdown('owner', $users, set_value('owner', $documents_owner),array(
										'class' => 'form-control'
									));
								?>
							</div>
						</div>
						<div class="box-footer">
							<button class="btn btn-sm btn-secondary" type="button" onclick="window.location.href='/reports/documents'">
								<?php echo lang('restore') ?>
							</button>
							<?php echo form_submit('s', lang('search'), array('class' => 'btn btn-sm btn-primary float-right')); ?>
						</div>
					</div>
					<?php
						echo form_close();
					?>
				</div>
				<div class="col-md-9">
					<div class="box">
						<div class="box-body no-padding">
							<?php if( empty($documents) ): ?>
								<div class="text-center">
									<img src="/resources/img/icons/no-results.svg"/>
									<p class="margin"><?php echo lang('no_results'); ?></p>
								</div>
							<?php else: ?>
								<div class="table-responsive mailbox-messages">
									<table class="table table-hover table-striped search" style="width:100%">
										<thead>
											<tr>
												<th><?php echo lang('documents_name'); ?></th>
												<th><?php echo lang('documents_type'); ?></th>
												<th><?php echo lang('documents_category'); ?></th>
												<th><?php echo lang('documents_owner'); ?></th>
												<th><?php echo lang('documents_edited_date'); ?></th>
												<th><?php echo lang('documents_valid_until'); ?></th>
											</tr>
										</thead>
										<tbody>
										<?php foreach($documents as $document): ?>
											<tr>
												<td data-order="<?php echo $document->document_name; ?>" data-search="<?php echo $document->document_name; ?>">
												<?php 
												$file_type = $document->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg"/>' :
												($document->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg"/>' :
												($document->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg"/>' : ''));
												echo $file_type . ' ' . safe_anchor('documents', $document->document_id, $document->document_name); ?></td>
												<td><div class="category-tag"><?php echo $documents_type[$document->document_type]; ?></div></td>
												<td><?php if ($document->document_category): ?>
													<div class="category-tag"><?php echo isset($documents_category[$document->document_category]) ? $documents_category[$document->document_category] : ''; ?></div>
													<?php endif; ?>
												</td>
												<td><?php echo username($document->document_owner); ?></td>
												<td><?php echo substr($document->edited_date, 0, 10); ?></td>
												<?php if( ! in_array($document->document_type,[1,2]) ): ?>
												<td data-order="<?php echo $document->document_valid_until; ?>" data-search="<?php echo $document->document_valid_until; ?>"><?php 
													$one_month = new DateInterval('P1M');
													$one_month_forward = new DateTime();
													$one_month_forward->add($one_month);
													$valid_until = date_create($document->document_valid_until);
													$flag = (new DateTime() > $valid_until ? "<img src='/resources/img/icons/error.svg' style='margin-left: -5px;
													margin-top: -4px;'/>" : 
													($one_month_forward > $valid_until ? 
													"<i class='fa fa-exclamation-triangle' style='color: #f39c12; margin-right: 5px;'></i>" : 
													"<i class='fa fa-info-circle' style='color: #00a65a; margin-right: 6px;'></i>"));
													echo $document->document_valid_until === '0000-00-00' ? '' : 
														($flag . ' ' . $document->document_valid_until) ; ?></td>
												<?php else: ?>
												<td data-order="0"></td>
												<?php endif; ?>
											</tr>
										<?php endforeach; ?>
										</tbody>
									</table>
									<!-- /.table -->
								</div>
								<!-- /.mail-box-messages -->
							<?php endif; ?>
						</div>
						<!-- /.box-body -->
					</div>
				</div>
				<!-- /.col-md-9-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');