<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<h1>
					<?php echo lang('reports'); ?>
					<small><?php echo lang('reports_' . $report['severity']); ?> > <?php echo lang('eventanalysis'); ?></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<?php // var_dump($sidebar['menu']['structure']); ?>
			<section class="content">
				<div class="row">
					<div class="col-md-12">
						<div class="box">
							<div class="box-header">
								<h3 class="box-title"><?php echo lang('eventanalysis_' . $report['severity']); ?></h3>
							</div>
							<!-- /.box-header -->
							<div class="box-body no-padding">
								<?php if( empty($eventanalysis) ): ?>
									<p class="text-center"><?php echo lang('folder_empty'); ?></p>
								<?php else: ?>
									<div class="table-responsive mailbox-messages">
										<table class="table table-hover table-striped">
											<thead>
												<tr>
													<th>Id</th>
													<th>Rubrik</th>
													<th>Redogörelse</th>
												</tr>
											</thead>
											<tbody>
											<?php foreach($eventanalysis as $e): ?>
												<tr>
													<td><?php echo safe_anchor('eventanalysis/saveqa', $events[$e->type_id]->id,substr($events[$e->type_id]->id,0,8).'...'); ?></td>
													<td><?php echo $events[$e->type_id]->name; ?></td>
													<td><?php echo substr($events[$e->type_id]->redo,0,100).'...'; ?></td>
												</tr>
											<?php endforeach; ?>
											</tbody>
										</table>
										<!-- /.table -->
									</div>
									<!-- /.mail-box-messages -->
								<?php endif; ?>
							</div>
							<!-- /.box-body -->
						</div>
						<!-- /. box -->
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
			</section>
			<!-- /.content -->
		</div>
		<!-- /.container -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>