<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<div class="float-right btn-group">
					<?php
					if( ! empty($rights['create']) )
					{
						echo anchor('eventanalysis/add','<i class="fa fa-plus" aria-hidden="true"></i>',
							array(
							'title' => 'Skapa en händelseanalys',
							'class' => 'btn btn-primary',
							)
						);
						// @TODO: Print
						echo form_button('','<i class="fa fa-print" aria-hidden="true"></i>',
							array(
							'title' => 'Skriv ut',
							'class' => 'btn btn-default',
							'onclick' => 'window.print()'
							)
						);
					}
					echo anchor('', '<i class="fa fa-home" aria-hidden="true"></i>',
						array(
						'title' => lang('home'),
						'class' => 'btn btn-default'
						)
					);
					?>
				</div>
				<h1>Händelseanalys
					<small></small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<section class="content">
				<div class="no-print">
					<div class="callout callout-info no-print" style="color: #172B4D !important;">
						När en avvikelse har inträffat är den ibland så allvarligt att lagen föreskriver att man skall göra en djupare händelseanalys än den som man gör i den vanliga avvikelsehanteringen.
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<?php if( ! empty($rights['read']) && !empty($report['eventanalysis']['critical']) ): ?>
						<div class="box">
							<div class="box-header with-border">
								<h3 class="box-title">
									Avvikelser som kräver en djupare händelseanalys
								</h3>
							</div>
							<div class="box-body no-padding">
								<table class="table table-striped no-data-table dated-table">
									<thead>
										<tr>
											<th>Rubrik</th>
											<th width="10%">Skapat datum</th>
											<th width="55px">&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										<?php
										foreach($report['eventanalysis']['critical'] as $deviation):
										?>
										<tr>
											<td><?php echo safe_anchor('deviation/view',$deviation->a_id,html_escape($deviation->title),
												array(
													'target' => '_blank'
												)
											); ?></td>
											<td style="min-width: 75px;"> <?php echo substr($deviation->reg_date_one, 0, 10) ?></td>
											<td>
												<div class="btn-group btn-group-sm">
													<?php
													echo icon_anchor('eventanalysis/add',$deviation->a_id,'<i class="fa fa-plus" aria-hidden="true"></i>',
														array(
														'title' => 'Skapa en händelseanalys',
														'class' => 'btn btn-primary',
														)
													);
													?>
												</div>
											</td>
										</tr>
										<?php endforeach; ?>
									</tbody>
								</table>
							</div>
						</div>
						<?php endif; ?>
						<div class="box">
							<div class="box-header with-border">
								<h3 class="box-title">
									Händelseanalyser
								</h3>
							</div>
							<div class="box-body no-padding">
								<?php
								if( ! empty($rights['read']) ):
									if(!empty($events)):
								?>
									<table class="table table-striped no-data-table dated-table">
										<thead>
											<tr>
												<th width="25%">Rubrik</th>
												<th width="10%">Skapat datum</th>
												<th>Redogörelse</th>
												<th width="10%">Färdig</th>
												<th width="85px">&nbsp;</th>
											</tr>
										</thead>
										<tbody>
											<?php
											foreach($events as $event):
												if(strlen($event->redo)>72) {
													$event->redo = substr($event->redo,0,72).'...';
												}
												$event->name?$event->name:'Ingen rubrik vald'
											?>
											<tr>
												<td><?php echo safe_anchor('eventanalysis/view',$event->id,$event->name); ?></td>
												<td style="min-width: 75px;"> <?php echo substr($event->created_date, 0, 10) ?></td>
												<td><?= $event->redo; ?></td>
												<td><?= $event->done?$event->done:''; ?></td>
												<?php if( ! empty($rights['update']) ) { ?>
												<td>
													<div class="btn-group btn-group-sm">
														<?php
														echo icon_anchor('eventanalysis/edit',$event->id,'<i class="fa fa-pencil" aria-hidden="true"></i>',
															array(
															'title' => 'Redigera',
															'class' => 'btn btn-default',
															)
														);
														
														echo icon_anchor('eventanalysis/connect',$event->id,'<i class="fa fa-link" aria-hidden="true"></i>',
															array(
															'title' => 'Anslut',
															'class' => 'btn btn-default',
															)
														);
														?>
													</div>
												</td>
												<?php } else { ?>
												<td></td>
												<?php } ?>
											</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
									<?php else: ?>
										<p class="margin">Du har inte skapat några händelseanalyser än.</p>
									<?php endif; ?>
								<?php else: ?>
									<p class="margin">Du har inte rättigheter att läsa händelseanalyser.</p>
								<?php endif; ?>
							</div>
						</div>
					</div>
					<!-- /.col-md-12-->
				</div>
				<!-- /.row -->
			</section>
			<!-- /section -->
		</div>
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');