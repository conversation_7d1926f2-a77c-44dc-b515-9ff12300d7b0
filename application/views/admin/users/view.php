<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/users/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('users_user'),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('users_users'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('users_users'); ?></h3>
						</div>
						<div class="box-body no-padding">
								<?php
								if( empty($users) )
								{
									lang('users_empty');
								}
								else
								{
								?>
								<table class="table table-striped no-data-table">
									<colspan>
										<col>
										<col width="180px;">
									</colspan>
									<tbody>
									<?php foreach($users as $user): ?>
										<tr>
											<td><?php echo safe_anchor('admin/users/update', $user->user_id, $user->name); ?></td>
											<td>
												<div class="btn-group btn-group-sm">
													<?php
													if ( $this->config->item('document_author') === FALSE )
														echo icon_anchor('admin/users/documents', $user->user_id ,'<i class="fa fa-files-o" aria-hidden="true"></i>',
														array(
														'title' => lang('users_documents'),
														'class' => 'btn btn-default',
														)
													);
													echo icon_anchor('admin/users/groups', $user->user_id ,'<i class="fa fa-users" aria-hidden="true"></i>',
														array(
														'title' => lang('users_groups'),
														'class' => 'btn btn-default',
														)
													);
													echo icon_anchor('admin/users/individual', $user->user_id ,'<i class="fa fa-user" aria-hidden="true"></i>',
														array(
														'title' => lang('users_individual'),
														'class' => 'btn btn-default',
														)
													);
													echo icon_anchor('admin/users/deviation', $user->user_id ,'<i class="fa fa-map-signs" aria-hidden="true"></i>',
														array(
														'title' => lang('users_deviation'),
														'class' => 'btn btn-default',
														)
													);
													echo icon_anchor('admin/users/security', $user->user_id ,'<i class="fa fa-lock" aria-hidden="true"></i>',
														array(
														'title' => lang('users_security'),
														'class' => 'btn btn-default',
														)
													);
													?>
												</div>
											</td>
										</tr>
									<?php endforeach; ?>
									</tbody>
								</table>
								<?php
								}
								?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');