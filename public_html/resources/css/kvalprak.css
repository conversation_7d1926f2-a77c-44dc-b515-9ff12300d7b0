html {
	overflow-y: scroll;
}

body {
  font-size: 14px;
}

.dropdown-menu {
  font-size: 14px;
}

.dropdown-menu>li>a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}

button {
  cursor: pointer;
}

.shadow-logo {
	max-width: 100%;
	opacity: 0.2;
}

.padding-10 {
	padding: 10px;
}

hr.small {
	margin: 0;
	border: 1px dashed #eee;
}

.cke_toolbox>.cke_toolbar+span+.cke_toolbar {
	display: none;
}

@media (max-width: 576px) {
	.cke_combo {
		display: none !important;
	}
}

@media (min-width: 1540px) {
	.cke_toolbar_break,.cke_button__hidetoolbar {
		display: none !important;
	}
	.cke_toolbox>.cke_toolbar+span+.cke_toolbar {
		display: block !important;
	}
}

.no-sidebar .content-wrapper,
.no-sidebar .main-footer{
	margin-left: 0;
}

.navbar-apps>li {
	float: left;
}

.nav>li.apps>a>span {
    position: relative;
    bottom: 6px;
}
.nav>li ul.app-box {
	padding: 8px 5px 0 3px;
	width: 650px !important;
  box-shadow: 0px 3px 5px rgb(9 30 66 / 20%), 0px 0px 1px rgb(9 30 66 / 31%);
  border-radius: 3px;
}

li.app-box  {
	margin: 0 0 0 5px;
}

.btn-app {
	min-width: 100px;
	margin: 0 0 8px 5px;
}

.btn-app>.badge {
	right: -3px;
}

li.apps .dropdown-menu {
	margin-top: 5px;
	margin-left: -23px;
}

@media (min-width: 768px) {
	.nav>li.apps>a {
		padding: 10px 15px;
	}
}

@media (max-width: 767px) {
	.main-header .nav>li>a {
		padding: 15px 12px;
	}
	.navbar-nav.navbar-apps {
		margin: 0;
	}
	.navbar-nav>li.apps {
		float: left;
	}
	.skin-blue .main-header .navbar .dropdown-menu.app-box a {
		color: inherit;
    max-width: 100px;
	}
	.skin-blue .main-header .navbar .dropdown-menu.app-box a:hover {
		background: inherit;
	}
}

@media (max-width: 991px) {
	.navbar-apps>li>a {
		padding-top: 15px;
		padding-bottom: 15px;
		line-height: 20px;
	}
	.main-header .navbar-custom-menu .app-box a {
		background: #f4f4f4;
    max-width: 100px;
	}
}

/* .sidebar {
  overflow-y: overlay !important;
} */

.sidebar {
  height: 95vh !important;
}

.sidebar-menu>li.document-treeview>a {
	padding: 12px 31px 12px 15px;
}

.document-treeview-menu>li.document-treeview>a {
	padding: 5px 30px 5px 15px;
}

.document-treeview-menu>li>a {
	padding: 8px 15px 8px 15px;
}

.sidebar-menu a {
    text-overflow: ellipsis;
    overflow: hidden;
}

.box-body.odd-even {
	padding: 0;
}

.box .form-group:last-child {
	margin-bottom: 0;
}

.form-group.odd {
    background-color: #e2e2e2;
    background-color: #f7f7f7;
}

.form-group.odd,
.form-group.even,
.form-control-table,
.form-control-description {
    padding: 10px 10px 10px;
	margin: 0;
}

.form-group.odd+.form-group.odd,
.form-group.even+.form-group.even {
	padding: 0px 10px 10px;
}

/* .form-group.odd+.form-group.odd label,
.form-group.even+.form-group.even label {
	font-weight: normal;
} */

.form-group.odd+.form-group.odd strong,
.form-group.even+.form-group.even strong {
	font-weight: normal;
	font-style: italic;
}

.form-control-sub-inline .radio {
	display: inline-block;
}

.form-control-sub-col .checkbox,
.form-control-sub-col .radio {
	margin-top: 0px;
	margin-bottom: 0px;
}

.form-control-sub-col .checkbox+.checkbox,
.form-control-sub-col .radio+.radio {
	margin-top: 5px;
}

.form-control-sub-col {
	padding-top: 5px;
	column-count: 2;
}

.table .form-control-sub-inline .radio {
	margin:0;
}

.form-group .table tbody td {
	vertical-align: middle;
}

.form-group.required>label:after {
  content:" *";
  color: red;
}

.form-group .checkbox.indent {
	margin-left: 15px;
}

.form-group p:last-child {
	margin-bottom: 0;
}

.container {
	padding-left: 0;
	padding-right: 0;
  width: 100%;
}

.free {
	background-color: #ecf7ed !important;
	border-left: 4px solid #46b450;
}

.description-list dd:not(:last-child) {
	border-bottom: 1px solid #eee;
	margin-bottom: 5px;
	padding-bottom: 5px;
}

.max-width-650 {
	max-width: 650px;
}

.content-logo {
	padding: 20px 20px 10px;
}

@media (min-width: 1290px) {
	.max-width-750 {
		max-width: 750px;
	}

	.max-width-1225 {
		max-width: 1225px;
	}
}

.col-md-8 .event {
	padding: 10px;
}

.col-md-8 .event:not(:last-child) {
	border-bottom: 1px solid #CCC;
}

.col-md-8 .event .eventChild {
	border-top: 1px solid #CCC;
	padding: 10px 0 0 20px;
	margin-bottom: 10px;
}

.col-md-8 .event:nth-child(even) {
	background-color: #f7f7f7;
}

@media (min-width: 992px) {
	section.content-header {
		position: fixed;
		right: 0;
		left: 0;
		margin-left: 250px;
		/* background: #FFF !important; */
		z-index: 800;
		/* border-bottom: 1px solid #d2d6de; */
		min-height: 54px;
		-webkit-transition: -webkit-transform .3s ease-in-out,margin .3s ease-in-out;
		-moz-transition: -moz-transform .3s ease-in-out,margin .3s ease-in-out;
		-o-transition: -o-transform .3s ease-in-out,margin .3s ease-in-out;
		transition: transform .3s ease-in-out,margin .3s ease-in-out;
	}

	.sidebar-collapse section.content-header {
    margin-left: 25px;
  }

	section.content {
		padding-top: 80px;
	}

	section.content.normal-padding {
		padding-top: 15px;
	}

	.content-header .float-right {
	    margin-top: -5px;
	}
}

section.content {
  padding-left: 25px;
}

@media (max-width: 767px) {
  section.content-header {
    margin-left: 20px;
  }
}

.notransition {
	-webkit-transition: none !important;
	-moz-transition: none !important;
	-o-transition: none !important;
	transition: none !important;
}

#ui-datepicker-div {
	z-index: 1000 !important;
}

.move.list-unstyled li {
	list-style-type: none;
}

.move.list-unstyled .radio {
	margin-top: 0;
	margin-bottom: 0;
}

.receipt {
	border: 1px dashed #b9b9b9;
	background-color: #f5f5f5;
	padding: 10px 20px 20px;
	width: 50%;
	margin: 0 auto 20px;
}

.receipt h4 {
	font-weight: bold;
	text-align: center;
}

.tableToggle:nth-child(4n+3) {
	background-color: #f9f9f9;
}

.tableToggle {
	cursor: pointer;
}

.tableToggleHidden {
	display: none;
}

.align-right {
	text-align: right;
}

/****************************************
	RISK ASSESMENTS
*****************************************/

.risks.box, .event.box, .eventChild.boxChild.box {
  background: #FFFFFF !important;
  box-shadow: 0px 3px 5px rgba(9, 30, 66, 0.2), 0px 0px 1px rgba(9, 30, 66, 0.31) !important;
  border-radius: 0.5rem;
}

.events {
  margin: 0 10px 1px 10px;
}

.risksTabel .riskok {
	background-color: #2CB54F;
	color: white;
}

.risksTabel .riskwarning {
	background-color: #FAFF11;
}

.risksTabel .riskdanger {
	background-color: #fd5701;
	color: white;
}

.risksTabel .riskmeltdown {
	background-color: #F50125;
	color: white;
}

/****************************************
	.box-body.no-padding.no-space
*****************************************/

.box-body.no-padding.no-space {
	margin-left: -1px;
	margin-right: -2px;
}

/****************************************
	.nav-box
*****************************************/

.nav-box {
	display: block;
	border-bottom: 1px solid #f4f4f4;
}

.nav-box>li {
	float: left;
}

.nav-box>li>a {
	border-right: 1px solid #f4f4f4;
}

/****************************************
	.file-upload
*****************************************/

.file-upload .form-control {
  min-height: 150px;
  outline: 1px dashed #ccc;
  outline-offset: -15px;
  background-color: #eee;
  padding: 12px;
}
.file-upload .form-control:before {
  content: "\f093";
  font: normal normal normal 14px/1 FontAwesome;
  font-size: 3em;
  left: 0;
  right: 0;
  display: block;
  margin: 20px auto 10px;
  text-align: center;
}
.file-upload .form-control:after {
  content: attr(data-message);
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  display: block;
}
.file-upload .form-control input[type="file"] {
  cursor: pointer;
  opacity: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
}

/****************************************
	Tree select
*****************************************/

.document-tree,
.document-tree-open {
    margin: 0 0 10px;
}
.document-tree ul,
.document-tree-open ul {
    list-style: none outside none;
    padding-left: 10px;
	margin: 0;
}
.document-tree li span,
.document-tree-open li span {
    line-height: 25px;
	font-size: 100%;
}
.document-tree span:hover,
.document-tree label:hover,
.document-tree-open span:hover,
.document-tree-open label:hover {
	cursor: pointer;
}
.document-tree > ul > li > span,
.document-tree-open > ul > li > label{
    color: #3B4C56;
    display: block;
    font-weight: normal;
    position: relative;
    text-decoration: none;
}
.document-tree li.parent > span,
.document-tree-open li.parent > span {
    padding: 0 0 0 28px;
}
.document-tree li.parent > span:before {
	font: normal normal normal 14px/1 FontAwesome;
    content: "\f0fe";
    display: block;
    left: 7px;
    position: absolute;
    top: 5px;
}
.document-tree ul li.active > span:before {
   content: "\f146";
}
.document-tree ul li ul,
.document-tree-open ul li ul {
    border-left: 1px solid #D9DADB;
    display: none;
    margin: 0 0 0 12px;
    overflow: hidden;
    padding: 0 0 0 25px;
}
.document-tree-open ul li ul {
	display: block;
}
.document-tree ul li ul li,
.document-tree-open ul li ul li {
    position: relative;
    font-size: 12px;
}
.document-tree ul li ul li:before,
.document-tree-open ul li ul li:before {
    border-bottom: 1px dashed #E2E2E3;
    content: "";
    left: -20px;
    position: absolute;
    top: 12px;
    width: 15px;
}

.document-tree input,
.document-tree-open input {
	top: 2px;
    position: relative;
    margin-right: 3px;
}

/****************************************
	Menu tree
*****************************************/

.table-menu .indent-one {
	border-left: 50px solid #ddd;
}

.table-menu .indent-two {
	border-left: 100px solid #ddd;
}

.table-menu .indent-two-folder {
	border-left: 100px solid #bbb;
}

/****************************************
	Comment
*****************************************/

.comment {
	padding: 10px;
}

.comment .author {
	font-weight: bold;
}

.comment:nth-child(even) {
	background-color: #f7f7f7;
}

/****************************************
	boostrap 4
*****************************************/

.font-weight-bold {
	font-weight: bold;
}

/****************************************
	Override adminlte
*****************************************/

.table-bordered>thead>tr>th, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>tbody>tr>td, .table-bordered>tfoot>tr>td {
	border: 1px solid #ddd;
}

/* .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
    border-top: 1px solid #ddd;
} */

.table-bordered {
	border: 1px solid #ddd;
}

.input-group-append>.btn:hover {
	z-index: 10 !important;
}

.main-header .sidebar-toggle:before {
    content: "\f03a";
}

.margin-bottom {
	margin-bottom: 10px;
}

.login-box-msg, .register-box-msg {
	padding: 0 0 20px 0;
}

.sidebar-menu>li>a {
	padding: 12px 30px 12px 15px;
}

.treeview-menu>li>a {
	padding: 5px 10px 5px 15px;
}

/****************************************
	Append adminlte
*****************************************/

.main-header .navbar-custom-menu {
  position: absolute;
  top: 0;
  right: 0;
}

.main-header .navbar-custom-menu-left {
  position: absolute;
  top: 0; 
  left: 0;
  background-color: #FFFFFF;
  height: 50px;
}

.tab-pane>.table {
  margin-bottom: 0;
}

td .btn-group-xs {
  margin-top: -6px;
  margin-bottom: -2px;
}

/* td .btn-group-sm  {
  margin-top: -10px;
  margin-bottom: -6px;
  right: -7px;
} */

td .table-options {
  float: right;
}

.d-none {
	display: none;
}

.main-sidebar .box {
  border-radius:0;
  box-shadow: none;
  margin-bottom: 0;
}

.main-sidebar.sidebar-bright {
  border-right: 1px solid #d2d6de;
  background-color: #FFF;
}

/****************************************
	.sortlist
*****************************************/
.sortlist .new-row {
  color: #00529B;
  background: #BDE5F8 !important;
}

/****************************************
	.sortlist
*****************************************/

.fieldWidget {
  min-height: 150px;
}

.fieldWidget .ui-state-highlight {
  height: 40px;
  border: none;
}

.fieldWidget li {
  position: relative;
  display: block;
  padding: 10px 15px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 75px;
}

.fieldWidget .btn-group {
  position: absolute;
  right: 5px;
  top: 5px;
}

.fieldWidget .required.red {
  color: #f39c12;
  font-weight: bold;
}

.fieldWidget .required.dark-red {
  color: #dd4b39;
  font-weight: bold;
}

/****************************************
	Overwrite jquery-ui
*****************************************/

.ui-state-highlight {
	border: 1px solid #dad55e;
	background: #fffa90 !important;
	color: #777620;
}

.ui-sortable-helper {
  margin: 10px 0;
  background: #FFF !important;
  border-left: 1px solid #ddd !important;
  border-right: 1px solid #ddd !important;
  border-bottom: 1px solid #ddd !important;
}

.ui-sortable-helper .btn-group {
  display: none;
}

/****************************************
	Alert
*****************************************/

.alert h1, .alert h2, .alert h3 {
  margin-top: 0;
}

/****************************************
	Dropzone
*****************************************/

/*
 * The MIT License
 * Copyright (c) 2012 Matias Meno <<EMAIL>>
 */
.dropzone, .dropzone * {
  box-sizing: border-box; }

.dropzone {
  position: relative; }
  .dropzone .dz-preview {
    position: relative;
    display: inline-block;
    width: 120px;
    margin: 0.5em; }
    .dropzone .dz-preview .dz-progress {
      display: block;
      height: 15px;
      border: 1px solid #aaa; }
      .dropzone .dz-preview .dz-progress .dz-upload {
        display: block;
        height: 100%;
        width: 0;
        background: green; }
    .dropzone .dz-preview .dz-error-message {
      color: red;
      display: none; }
    .dropzone .dz-preview.dz-error .dz-error-message, .dropzone .dz-preview.dz-error .dz-error-mark {
      display: block; }
    .dropzone .dz-preview.dz-success .dz-success-mark {
      display: block; }
    .dropzone .dz-preview .dz-error-mark, .dropzone .dz-preview .dz-success-mark {
      position: absolute;
      display: none;
      left: 30px;
      top: 30px;
      width: 54px;
      height: 58px;
      left: 50%;
      margin-left: -27px; }

@-webkit-keyframes passing-through {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -moz-transform: translateY(40px);
    -ms-transform: translateY(40px);
    -o-transform: translateY(40px);
    transform: translateY(40px); }
  30%, 70% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px); }
  100% {
    opacity: 0;
    -webkit-transform: translateY(-40px);
    -moz-transform: translateY(-40px);
    -ms-transform: translateY(-40px);
    -o-transform: translateY(-40px);
    transform: translateY(-40px); } }
@-moz-keyframes passing-through {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -moz-transform: translateY(40px);
    -ms-transform: translateY(40px);
    -o-transform: translateY(40px);
    transform: translateY(40px); }
  30%, 70% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px); }
  100% {
    opacity: 0;
    -webkit-transform: translateY(-40px);
    -moz-transform: translateY(-40px);
    -ms-transform: translateY(-40px);
    -o-transform: translateY(-40px);
    transform: translateY(-40px); } }
@keyframes passing-through {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -moz-transform: translateY(40px);
    -ms-transform: translateY(40px);
    -o-transform: translateY(40px);
    transform: translateY(40px); }
  30%, 70% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px); }
  100% {
    opacity: 0;
    -webkit-transform: translateY(-40px);
    -moz-transform: translateY(-40px);
    -ms-transform: translateY(-40px);
    -o-transform: translateY(-40px);
    transform: translateY(-40px); } }
@-webkit-keyframes slide-in {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -moz-transform: translateY(40px);
    -ms-transform: translateY(40px);
    -o-transform: translateY(40px);
    transform: translateY(40px); }
  30% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px); } }
@-moz-keyframes slide-in {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -moz-transform: translateY(40px);
    -ms-transform: translateY(40px);
    -o-transform: translateY(40px);
    transform: translateY(40px); }
  30% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px); } }
@keyframes slide-in {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -moz-transform: translateY(40px);
    -ms-transform: translateY(40px);
    -o-transform: translateY(40px);
    transform: translateY(40px); }
  30% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px); } }
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); }
  10% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1); }
  20% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); } }
@-moz-keyframes pulse {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); }
  10% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1); }
  20% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); } }
@keyframes pulse {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); }
  10% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1); }
  20% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); } }
.dropzone, .dropzone * {
  box-sizing: border-box; }

.dropzone {
  min-height: 150px;
  height: auto;
  background: white; }
  .dropzone.dz-clickable {
    cursor: pointer; }
    .dropzone.dz-clickable * {
      cursor: default; }
    .dropzone.dz-clickable .dz-message, .dropzone.dz-clickable .dz-message * {
      cursor: pointer; }
  .dropzone.dz-started .dz-message {
    display: none; }
  .dropzone.dz-drag-hover {
    border-style: solid; }
    .dropzone.dz-drag-hover .dz-message {
      opacity: 0.5; }
  .dropzone .dz-message {
    text-align: center;
    margin: 1em 0; }
  .dropzone .dz-preview {
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 13px;
    min-height: 100px; }
    .dropzone .dz-preview:hover {
      z-index: 1000; }
      .dropzone .dz-preview:hover .dz-details {
        opacity: 1; }
    .dropzone .dz-preview.dz-file-preview .dz-image {
      border-radius: 20px;
      background: #999;
      background: linear-gradient(to bottom, #eee, #ddd); }
    .dropzone .dz-preview.dz-file-preview .dz-details {
      opacity: 1; }
      .dropzone .dz-preview.dz-image-preview .dz-details {
        -webkit-transition: opacity 0.2s linear;
        -moz-transition: opacity 0.2s linear;
        -ms-transition: opacity 0.2s linear;
        -o-transition: opacity 0.2s linear;
        transition: opacity 0.2s linear; }
    .dropzone .dz-preview .dz-remove {
      font-size: 14px;
      text-align: center;
      display: block;
      cursor: pointer;
      border: none; }
      .dropzone .dz-preview .dz-remove:hover {
        text-decoration: underline; }
    .dropzone .dz-preview:hover .dz-details {
      opacity: 1; }
    .dropzone .dz-preview .dz-details {
      z-index: 20;
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      font-size: 13px;
      min-width: 100%;
      max-width: 100%;
      padding: 2em 1em;
      text-align: center;
      color: rgba(0, 0, 0, 0.9);
      line-height: 150%; }
      .dropzone .dz-preview .dz-details .dz-size {
        margin-bottom: 1em;
        font-size: 16px; }
      .dropzone .dz-preview .dz-details .dz-filename {
        white-space: nowrap; }
        .dropzone .dz-preview .dz-details .dz-filename:hover span {
          border: 1px solid rgba(200, 200, 200, 0.8);
          background-color: rgba(255, 255, 255, 0.8); }
        .dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
          overflow: hidden;
          text-overflow: ellipsis; }
          .dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
            border: 1px solid transparent; }
      .dropzone .dz-preview .dz-details .dz-filename span, .dropzone .dz-preview .dz-details .dz-size span {
        background-color: rgba(255, 255, 255, 0.4);
        padding: 0 0.4em;
        border-radius: 3px; }
    .dropzone .dz-preview:hover .dz-image img {
      -webkit-transform: scale(1.05, 1.05);
      -moz-transform: scale(1.05, 1.05);
      -ms-transform: scale(1.05, 1.05);
      -o-transform: scale(1.05, 1.05);
      transform: scale(1.05, 1.05);
      -webkit-filter: blur(8px);
      filter: blur(8px); }
    .dropzone .dz-preview .dz-image {
      border-radius: 20px;
      overflow: hidden;
      width: 120px;
      height: 120px;
      position: relative;
      display: block;
      z-index: 10; }
      .dropzone .dz-preview .dz-image img {
        display: block; }
    .dropzone .dz-preview.dz-success .dz-success-mark {
      -webkit-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
      -moz-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
      -ms-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
      -o-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
      animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1); }
    .dropzone .dz-preview.dz-error .dz-error-mark {
      opacity: 1;
      -webkit-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
      -moz-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
      -ms-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
      -o-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
      animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1); }
    .dropzone .dz-preview .dz-success-mark, .dropzone .dz-preview .dz-error-mark {
      pointer-events: none;
      opacity: 0;
      z-index: 500;
      position: absolute;
      display: block;
      top: 50%;
      left: 50%;
      margin-left: -27px;
      margin-top: -27px; }
      .dropzone .dz-preview .dz-success-mark svg, .dropzone .dz-preview .dz-error-mark svg {
        display: block;
        width: 54px;
        height: 54px; }
    .dropzone .dz-preview.dz-processing .dz-progress {
      opacity: 1;
      -webkit-transition: all 0.2s linear;
      -moz-transition: all 0.2s linear;
      -ms-transition: all 0.2s linear;
      -o-transition: all 0.2s linear;
      transition: all 0.2s linear; }
    .dropzone .dz-preview.dz-complete .dz-progress {
      opacity: 0;
      -webkit-transition: opacity 0.4s ease-in;
      -moz-transition: opacity 0.4s ease-in;
      -ms-transition: opacity 0.4s ease-in;
      -o-transition: opacity 0.4s ease-in;
      transition: opacity 0.4s ease-in; }
    .dropzone .dz-preview:not(.dz-processing) .dz-progress {
      -webkit-animation: pulse 6s ease infinite;
      -moz-animation: pulse 6s ease infinite;
      -ms-animation: pulse 6s ease infinite;
      -o-animation: pulse 6s ease infinite;
      animation: pulse 6s ease infinite; }
    .dropzone .dz-preview .dz-progress {
      opacity: 1;
      z-index: 1000;
      pointer-events: none;
      position: absolute;
      height: 16px;
      left: 50%;
      top: 50%;
      margin-top: -8px;
      width: 80px;
      margin-left: -40px;
      background: rgba(255, 255, 255, 0.9);
      -webkit-transform: scale(1);
      border-radius: 8px;
      overflow: hidden; }
      .dropzone .dz-preview .dz-progress .dz-upload {
        background: #333;
        background: linear-gradient(to bottom, #666, #444);
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 0;
        -webkit-transition: width 300ms ease-in-out;
        -moz-transition: width 300ms ease-in-out;
        -ms-transition: width 300ms ease-in-out;
        -o-transition: width 300ms ease-in-out;
        transition: width 300ms ease-in-out; }
    .dropzone .dz-preview.dz-error .dz-error-message {
      display: block; }
    .dropzone .dz-preview.dz-error:hover .dz-error-message {
      opacity: 1;
      pointer-events: auto; }
    .dropzone .dz-preview .dz-error-message {
      pointer-events: none;
      z-index: 1000;
      position: absolute;
      display: block;
      display: none;
      opacity: 0;
      -webkit-transition: opacity 0.3s ease;
      -moz-transition: opacity 0.3s ease;
      -ms-transition: opacity 0.3s ease;
      -o-transition: opacity 0.3s ease;
      transition: opacity 0.3s ease;
      border-radius: 8px;
      font-size: 13px;
      top: 130px;
      left: -10px;
      width: 140px;
      background: #be2626;
      background: linear-gradient(to bottom, #be2626, #a92222);
      padding: 0.5em 1.2em;
      color: white; }
      .dropzone .dz-preview .dz-error-message:after {
        content: '';
        position: absolute;
        top: -6px;
        left: 64px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid #be2626; }

/****************************************
	Print
*****************************************/

@media not print {
  .show-in-print {
    display: none !important;
  }
}

/****************************************
	RIGHT SIDEBAR LAYOUT FIX
*****************************************/

/* Base Sidebar Positioning */
#sidebar-wrapper {
    position: fixed;
    top: 50px; /* Below the header */
    right: 0;
    width: 350px;
    height: calc(100vh - 50px);
    background: #fff;
    border-left: 1px solid #d2d6de;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 900;
    overflow-y: auto;
    padding: 15px;
    transform: translateX(100%); /* Hidden by default */
    transition: transform 0.3s ease-in-out;
}

/* Show sidebar with animation */
#sidebar-wrapper.show {
    transform: translateX(0);
}

/* Content Area Adjustments - When left sidebar is open (default state) */
body:not(.sidebar-collapse) .content-wrapper.sidebar-right-open {
    margin-right: 350px;
}

/* Content Area Adjustments - When left sidebar is collapsed */
body.sidebar-collapse .content-wrapper.sidebar-right-open {
    margin-right: 350px;
}

/* Mobile Responsive Behavior */
@media (max-width: 991px) {
    #sidebar-wrapper {
        width: 100%;
        transform: translateX(100%);
    }

    /* On mobile, overlay instead of pushing content */
    .content-wrapper.sidebar-right-open {
        margin-right: 0;
    }
}

/* Document Info Box Styling */
.document-info-box-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f4f4f4;
    cursor: default;
}

.document-info-box-title .fa-times {
    cursor: pointer;
    color: #999;
    font-size: 16px;
}

.document-info-box-title .fa-times:hover {
    color: #333;
}

/* Description List Styling in Sidebar */
#sidebar-wrapper .description-list {
    margin: 0;
}

#sidebar-wrapper .description-list dt {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

#sidebar-wrapper .description-list dd {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

#sidebar-wrapper .description-list dd:not(:last-child) {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

@media print {
  .show-in-print {
    display: block !important;
  }
	a[href]:after {
		content: none !important;
	}

	.box,
	.box-body
	{
		border:none;
		margin: 0 !important;
		padding: 0 !important;
	}

	.content {
		padding-bottom: 0 !important;
	}

	.form-text {
		display: none !important;
	}

	.description-list {
		column-count: 2;
	}

	.description-list dt {
		float: left;
		margin-right: 3px;
	}

	.description-list dd:not(:last-child) {
		border-bottom: none;
		padding-bottom: 0;
		margin-bottom: 0;
	}

	body.document-editor {
		margin-bottom: 0 !important;
	}

}

.loader {
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  width: 120px;
  height: 120px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
  margin-left: 40%;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

label {
  font-weight: 700;
}

.form-control {
  font-size: 14px;
}

.form-check label {
  font-weight: 400;
}

label {
  margin-bottom: 5px;
}

.form-text {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373;
}

.h4, h4 {
  font-size: 18px;
}

.h4 .small, .h4 small, .h5 .small, .h5 small, .h6 .small, .h6 small, h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {
  font-size: 75%;
}

.input-group-prepend {
  align-items: center;
  padding: 6px 12px;
  color: #555;
  text-align: center;
  background-color: #eee;
  border: 1px solid #ccc;
}

.input-group-prepend > .fa, .user-footer > div > a > .fa {
  font-size: 16px;
  line-height: inherit;
}

.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none;
}

.home-header {
  border-bottom: 1px solid rgb(229 231 235);
}

@media (min-width: 768px) {
  .home-header {
    padding-bottom: 10px;
  }
}

.home-section {
  border: 1px solid rgb(233 213 255);
  border-radius: 0.5rem;
  margin-left: 0;
  margin-right: 0;
  box-shadow: 0px 3px 5px 0px #00000040;
}

/* Force text wrapping for long words in fieldWidget items */
.fieldWidget li {
	word-wrap: break-word !important;
	word-break: break-all !important;
	overflow-wrap: anywhere !important;
	white-space: normal !important;
}

